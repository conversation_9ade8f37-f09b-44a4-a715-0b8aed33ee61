#!/usr/bin/env python3
"""
Background Workers for ScalperGPT GUI
Implements QThread-based background processing to maintain UI responsiveness
"""

import sys
import os
import time
import psutil
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# PyQt5 imports
from PyQt5.QtCore import QThread, QObject, pyqtSignal, QTimer, QMutex, QWaitCondition

@dataclass
class WorkerTask:
    """Represents a background task"""
    task_id: str
    function: Callable
    args: tuple
    kwargs: dict
    priority: int = 1  # 1=high, 2=medium, 3=low
    timeout: float = 30.0
    retry_count: int = 0
    max_retries: int = 3

class DataRefreshWorker(QThread):
    """Background worker for refreshing market data and system metrics"""
    
    # Signals
    data_updated = pyqtSignal(dict)  # Updated data
    error_occurred = pyqtSignal(str)  # Error message
    status_changed = pyqtSignal(str)  # Status update
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.running = False
        self.refresh_interval = 1.0  # seconds
        self.trading_interface = None
        self.last_update = datetime.now()
        
        # Data cache
        self.cached_data = {
            'positions': [],
            'orders': [],
            'balance': 0.0,
            'system_health': {},
            'market_data': {},
            'performance_metrics': {}
        }
    
    def set_trading_interface(self, interface):
        """Set reference to trading interface"""
        self.trading_interface = interface
    
    def set_refresh_interval(self, interval: float):
        """Set refresh interval in seconds"""
        self.refresh_interval = max(0.1, interval)
    
    def run(self):
        """Main worker thread loop"""
        self.running = True
        self.status_changed.emit("Data refresh worker started")
        
        while self.running:
            try:
                # Refresh all data
                updated_data = self.refresh_all_data()
                
                # Emit updated data
                if updated_data:
                    self.data_updated.emit(updated_data)
                    self.last_update = datetime.now()
                
                # Sleep for refresh interval
                self.msleep(int(self.refresh_interval * 1000))
                
            except Exception as e:
                self.error_occurred.emit(f"Data refresh error: {e}")
                self.msleep(5000)  # Wait 5 seconds on error
    
    def refresh_all_data(self) -> Dict[str, Any]:
        """Refresh all data sources"""
        updated_data = {}
        
        try:
            # Refresh positions
            positions = self.refresh_positions()
            if positions is not None:
                updated_data['positions'] = positions
                self.cached_data['positions'] = positions
            
            # Refresh orders
            orders = self.refresh_orders()
            if orders is not None:
                updated_data['orders'] = orders
                self.cached_data['orders'] = orders
            
            # Refresh system health
            health = self.refresh_system_health()
            if health is not None:
                updated_data['system_health'] = health
                self.cached_data['system_health'] = health
            
            # Refresh market data
            market_data = self.refresh_market_data()
            if market_data is not None:
                updated_data['market_data'] = market_data
                self.cached_data['market_data'] = market_data
            
            return updated_data
            
        except Exception as e:
            self.error_occurred.emit(f"Error refreshing data: {e}")
            return {}
    
    def refresh_positions(self) -> Optional[List[Dict]]:
        """Refresh trading positions"""
        try:
            if self.trading_interface and hasattr(self.trading_interface, 'get_positions'):
                return self.trading_interface.get_positions()
            
            # Fallback: try to get positions from launch_epinnox
            try:
                from launch_epinnox import fetch_open_positions
                return fetch_open_positions()
            except ImportError:
                pass
            
            return []
            
        except Exception as e:
            self.error_occurred.emit(f"Error refreshing positions: {e}")
            return None
    
    def refresh_orders(self) -> Optional[List[Dict]]:
        """Refresh open orders"""
        try:
            if self.trading_interface and hasattr(self.trading_interface, 'get_orders'):
                return self.trading_interface.get_orders()
            
            # Fallback: try to get orders from launch_epinnox
            try:
                from launch_epinnox import fetch_open_orders
                return fetch_open_orders()
            except ImportError:
                pass
            
            return []
            
        except Exception as e:
            self.error_occurred.emit(f"Error refreshing orders: {e}")
            return None
    
    def refresh_system_health(self) -> Optional[Dict]:
        """Refresh system health metrics"""
        try:
            health = {
                'connection_status': 'Connected' if self.trading_interface else 'Disconnected',
                'last_update': datetime.now(),
                'cpu_usage': psutil.cpu_percent(interval=None),
                'memory_usage': psutil.virtual_memory().percent,
                'api_latency': self.measure_api_latency(),
                'error_count': 0,  # TODO: Implement error tracking
                'uptime': datetime.now() - self.last_update
            }
            
            return health
            
        except Exception as e:
            self.error_occurred.emit(f"Error refreshing system health: {e}")
            return None
    
    def refresh_market_data(self) -> Optional[Dict]:
        """Refresh market data"""
        try:
            # Get current symbol from trading interface
            symbol = "DOGE/USDT:USDT"  # Default symbol
            
            if self.trading_interface and hasattr(self.trading_interface, 'current_symbol'):
                symbol = self.trading_interface.current_symbol
            
            # Fetch market data
            try:
                from launch_epinnox import fetch_order_book, exchange
                if exchange:
                    ticker = exchange.fetch_ticker(symbol)
                    orderbook = fetch_order_book(symbol)
                    
                    return {
                        'symbol': symbol,
                        'price': ticker.get('last', 0),
                        'bid': ticker.get('bid', 0),
                        'ask': ticker.get('ask', 0),
                        'volume': ticker.get('baseVolume', 0),
                        'change': ticker.get('change', 0),
                        'percentage': ticker.get('percentage', 0),
                        'orderbook': orderbook
                    }
            except ImportError:
                pass
            
            return {}
            
        except Exception as e:
            self.error_occurred.emit(f"Error refreshing market data: {e}")
            return None
    
    def measure_api_latency(self) -> float:
        """Measure API latency"""
        try:
            start_time = time.time()
            
            # Try to make a simple API call
            try:
                from launch_epinnox import exchange
                if exchange:
                    exchange.fetch_ticker("BTC/USDT:USDT")
                    return (time.time() - start_time) * 1000  # Convert to milliseconds
            except:
                pass
            
            return 0.0
            
        except Exception:
            return 999.0  # High latency indicates error
    
    def stop(self):
        """Stop the worker thread"""
        self.running = False
        self.status_changed.emit("Data refresh worker stopped")

class LLMAnalysisWorker(QThread):
    """Background worker for LLM analysis to prevent UI blocking"""
    
    # Signals
    analysis_completed = pyqtSignal(dict)  # Analysis results
    analysis_failed = pyqtSignal(str)  # Error message
    progress_updated = pyqtSignal(int)  # Progress percentage
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.analysis_queue = []
        self.running = False
        self.llm_interface = None
        self.mutex = QMutex()
        self.condition = QWaitCondition()
    
    def set_llm_interface(self, interface):
        """Set reference to LLM interface"""
        self.llm_interface = interface
    
    def queue_analysis(self, analysis_type: str, data: Dict[str, Any], priority: int = 1):
        """Queue an analysis task"""
        task = {
            'type': analysis_type,
            'data': data,
            'priority': priority,
            'timestamp': datetime.now()
        }
        
        self.mutex.lock()
        self.analysis_queue.append(task)
        self.analysis_queue.sort(key=lambda x: x['priority'])  # Sort by priority
        self.condition.wakeOne()
        self.mutex.unlock()
    
    def run(self):
        """Main LLM analysis thread loop"""
        self.running = True
        
        while self.running:
            self.mutex.lock()
            
            # Wait for tasks if queue is empty
            if not self.analysis_queue:
                self.condition.wait(self.mutex, 5000)  # Wait up to 5 seconds
            
            # Get next task
            task = None
            if self.analysis_queue:
                task = self.analysis_queue.pop(0)
            
            self.mutex.unlock()
            
            # Process task
            if task:
                self.process_analysis_task(task)
    
    def process_analysis_task(self, task: Dict[str, Any]):
        """Process a single analysis task"""
        try:
            analysis_type = task['type']
            data = task['data']
            
            self.progress_updated.emit(10)  # Starting analysis
            
            # Perform analysis based on type
            if analysis_type == 'market_analysis':
                result = self.perform_market_analysis(data)
            elif analysis_type == 'risk_assessment':
                result = self.perform_risk_assessment(data)
            elif analysis_type == 'opportunity_scan':
                result = self.perform_opportunity_scan(data)
            else:
                raise ValueError(f"Unknown analysis type: {analysis_type}")
            
            self.progress_updated.emit(100)  # Analysis complete
            
            # Emit results
            self.analysis_completed.emit({
                'type': analysis_type,
                'result': result,
                'timestamp': datetime.now()
            })
            
        except Exception as e:
            self.analysis_failed.emit(f"Analysis failed: {e}")
    
    def perform_market_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform market analysis"""
        # Simulate LLM analysis
        self.progress_updated.emit(50)
        time.sleep(0.5)  # Simulate processing time
        
        return {
            'trend': 'bullish',
            'confidence': 0.75,
            'signals': ['volume_increase', 'price_breakout'],
            'recommendation': 'Consider long position'
        }
    
    def perform_risk_assessment(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform risk assessment"""
        self.progress_updated.emit(50)
        time.sleep(0.3)
        
        return {
            'risk_level': 'medium',
            'max_position_size': 0.02,
            'stop_loss': 0.03,
            'take_profit': 0.06
        }
    
    def perform_opportunity_scan(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform opportunity scanning"""
        self.progress_updated.emit(50)
        time.sleep(0.7)
        
        return {
            'opportunities': [
                {'symbol': 'BTC/USDT:USDT', 'score': 85, 'type': 'breakout'},
                {'symbol': 'ETH/USDT:USDT', 'score': 72, 'type': 'reversal'}
            ],
            'total_scanned': 50,
            'high_quality_count': 2
        }
    
    def stop(self):
        """Stop the worker thread"""
        self.running = False
        self.condition.wakeAll()

class TaskSchedulerWorker(QThread):
    """Background worker for scheduling and managing tasks"""
    
    # Signals
    task_completed = pyqtSignal(str, dict)  # task_id, result
    task_failed = pyqtSignal(str, str)  # task_id, error
    queue_status_changed = pyqtSignal(int)  # queue_length
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.task_queue = []
        self.running = False
        self.mutex = QMutex()
        self.condition = QWaitCondition()
        self.max_concurrent_tasks = 3
        self.active_tasks = {}
    
    def schedule_task(self, task: WorkerTask):
        """Schedule a task for execution"""
        self.mutex.lock()
        self.task_queue.append(task)
        self.task_queue.sort(key=lambda x: x.priority)
        self.queue_status_changed.emit(len(self.task_queue))
        self.condition.wakeOne()
        self.mutex.unlock()
    
    def run(self):
        """Main task scheduler loop"""
        self.running = True
        
        while self.running:
            self.mutex.lock()
            
            # Wait for tasks if queue is empty
            if not self.task_queue:
                self.condition.wait(self.mutex, 1000)
            
            # Get next task if we have capacity
            task = None
            if (self.task_queue and 
                len(self.active_tasks) < self.max_concurrent_tasks):
                task = self.task_queue.pop(0)
                self.queue_status_changed.emit(len(self.task_queue))
            
            self.mutex.unlock()
            
            # Execute task
            if task:
                self.execute_task(task)
    
    def execute_task(self, task: WorkerTask):
        """Execute a single task"""
        try:
            # Add to active tasks
            self.active_tasks[task.task_id] = task
            
            # Execute the task function
            result = task.function(*task.args, **task.kwargs)
            
            # Remove from active tasks
            del self.active_tasks[task.task_id]
            
            # Emit success
            self.task_completed.emit(task.task_id, result)
            
        except Exception as e:
            # Remove from active tasks
            if task.task_id in self.active_tasks:
                del self.active_tasks[task.task_id]
            
            # Handle retries
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                self.schedule_task(task)  # Reschedule
            else:
                self.task_failed.emit(task.task_id, str(e))
    
    def stop(self):
        """Stop the task scheduler"""
        self.running = False
        self.condition.wakeAll()

class ResponsiveUIManager(QObject):
    """Manager for maintaining UI responsiveness during heavy operations"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Initialize workers
        self.data_worker = DataRefreshWorker()
        self.llm_worker = LLMAnalysisWorker()
        self.task_worker = TaskSchedulerWorker()
        
        # Start workers
        self.data_worker.start()
        self.llm_worker.start()
        self.task_worker.start()
        
        print("✅ Responsive UI Manager initialized with background workers")
    
    def set_trading_interface(self, interface):
        """Set trading interface for data worker"""
        self.data_worker.set_trading_interface(interface)
    
    def set_llm_interface(self, interface):
        """Set LLM interface for analysis worker"""
        self.llm_worker.set_llm_interface(interface)
    
    def queue_llm_analysis(self, analysis_type: str, data: Dict[str, Any], priority: int = 1):
        """Queue LLM analysis task"""
        self.llm_worker.queue_analysis(analysis_type, data, priority)
    
    def schedule_background_task(self, task: WorkerTask):
        """Schedule background task"""
        self.task_worker.schedule_task(task)
    
    def shutdown(self):
        """Shutdown all workers"""
        print("🔄 Shutting down background workers...")
        
        self.data_worker.stop()
        self.llm_worker.stop()
        self.task_worker.stop()
        
        self.data_worker.wait(5000)
        self.llm_worker.wait(5000)
        self.task_worker.wait(5000)
        
        print("✅ Background workers shutdown complete")
