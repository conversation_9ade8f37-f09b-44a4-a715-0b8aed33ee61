#!/usr/bin/env python3
"""
ScalperGPT Real-Time Dashboard & Trade Control
Comprehensive dashboard for monitoring and manual trading controls
"""

import sys
import os
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# PyQt5 imports
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import pyqtgraph as pg

# Import Matrix theme
try:
    from gui.matrix_theme import MatrixTheme
except ImportError:
    from matrix_theme import MatrixTheme

@dataclass
class PositionInfo:
    """Information about a trading position"""
    symbol: str
    side: str  # 'long' or 'short'
    size: float
    entry_price: float
    current_price: float
    unrealized_pnl: float
    percentage: float
    timestamp: datetime

@dataclass
class OrderInfo:
    """Information about a trading order"""
    id: str
    symbol: str
    side: str  # 'buy' or 'sell'
    type: str  # 'limit', 'market', 'stop'
    amount: float
    price: float
    status: str
    timestamp: datetime

@dataclass
class SystemHealth:
    """System health metrics"""
    connection_status: str
    last_update: datetime
    cpu_usage: float
    memory_usage: float
    api_latency: float
    error_count: int
    uptime: timedelta

class RealTimeDashboard(QWidget):
    """Real-time dashboard for monitoring trading system"""
    
    # Signals
    manual_trade_requested = pyqtSignal(dict)  # trade_params
    emergency_stop_requested = pyqtSignal()
    system_restart_requested = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.positions = []
        self.orders = []
        self.system_health = SystemHealth(
            connection_status="Disconnected",
            last_update=datetime.now(),
            cpu_usage=0.0,
            memory_usage=0.0,
            api_latency=0.0,
            error_count=0,
            uptime=timedelta()
        )
        
        self.setup_ui()
        self.setup_update_timer()
    
    def setup_ui(self):
        """Setup the dashboard UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Title
        title_label = QLabel("📊 REAL-TIME TRADING DASHBOARD")
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {MatrixTheme.LIGHT_GREEN};
                font-size: 18px;
                font-weight: bold;
                padding: 10px;
                border-bottom: 2px solid {MatrixTheme.GREEN};
                margin-bottom: 15px;
            }}
        """)
        layout.addWidget(title_label)
        
        # Create main content areas
        content_splitter = QSplitter(Qt.Horizontal)
        
        # Left panel: Positions and Orders
        left_panel = self.create_positions_orders_panel()
        content_splitter.addWidget(left_panel)
        
        # Right panel: System Health and Controls
        right_panel = self.create_health_controls_panel()
        content_splitter.addWidget(right_panel)
        
        # Set splitter proportions
        content_splitter.setSizes([600, 400])
        layout.addWidget(content_splitter)
        
        # Bottom panel: Manual Trading Controls
        manual_controls = self.create_manual_trading_controls()
        layout.addWidget(manual_controls)
    
    def create_positions_orders_panel(self) -> QWidget:
        """Create positions and orders monitoring panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Positions section
        positions_group = QGroupBox("📈 Current Positions")
        positions_layout = QVBoxLayout(positions_group)
        
        self.positions_table = QTableWidget()
        self.positions_table.setColumnCount(7)
        self.positions_table.setHorizontalHeaderLabels([
            "Symbol", "Side", "Size", "Entry Price", "Current Price", "PnL", "PnL %"
        ])
        self.positions_table.horizontalHeader().setStretchLastSection(True)
        positions_layout.addWidget(self.positions_table)
        
        # Orders section
        orders_group = QGroupBox("📋 Open Orders")
        orders_layout = QVBoxLayout(orders_group)
        
        self.orders_table = QTableWidget()
        self.orders_table.setColumnCount(7)
        self.orders_table.setHorizontalHeaderLabels([
            "ID", "Symbol", "Side", "Type", "Amount", "Price", "Status"
        ])
        self.orders_table.horizontalHeader().setStretchLastSection(True)
        orders_layout.addWidget(self.orders_table)
        
        # Add to layout
        layout.addWidget(positions_group)
        layout.addWidget(orders_group)
        
        return panel
    
    def create_health_controls_panel(self) -> QWidget:
        """Create system health and emergency controls panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # System Health section
        health_group = QGroupBox("🔧 System Health")
        health_layout = QVBoxLayout(health_group)
        
        # Health metrics
        self.health_labels = {}
        health_metrics = [
            ("Connection", "connection_status"),
            ("Last Update", "last_update"),
            ("CPU Usage", "cpu_usage"),
            ("Memory Usage", "memory_usage"),
            ("API Latency", "api_latency"),
            ("Error Count", "error_count"),
            ("Uptime", "uptime")
        ]
        
        for label_text, metric_key in health_metrics:
            metric_layout = QHBoxLayout()
            
            label = QLabel(f"{label_text}:")
            label.setMinimumWidth(100)
            label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")
            
            value_label = QLabel("--")
            value_label.setStyleSheet(f"color: {MatrixTheme.LIGHT_GREEN};")
            self.health_labels[metric_key] = value_label
            
            metric_layout.addWidget(label)
            metric_layout.addWidget(value_label)
            metric_layout.addStretch()
            
            health_layout.addLayout(metric_layout)
        
        # Emergency Controls section
        controls_group = QGroupBox("🚨 Emergency Controls")
        controls_layout = QVBoxLayout(controls_group)
        
        # Emergency Stop button
        emergency_stop_btn = QPushButton("🛑 EMERGENCY STOP")
        emergency_stop_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {MatrixTheme.RED};
                color: {MatrixTheme.WHITE};
                font-weight: bold;
                font-size: 14px;
                padding: 10px;
                border: 2px solid {MatrixTheme.RED};
                border-radius: 5px;
            }}
            QPushButton:hover {{
                background-color: #FF3333;
            }}
        """)
        emergency_stop_btn.clicked.connect(self.emergency_stop_requested.emit)
        
        # System Restart button
        restart_btn = QPushButton("🔄 Restart System")
        restart_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {MatrixTheme.YELLOW};
                color: {MatrixTheme.BLACK};
                font-weight: bold;
                padding: 8px;
                border: 2px solid {MatrixTheme.YELLOW};
                border-radius: 5px;
            }}
        """)
        restart_btn.clicked.connect(self.system_restart_requested.emit)
        
        controls_layout.addWidget(emergency_stop_btn)
        controls_layout.addWidget(restart_btn)
        
        # Add to layout
        layout.addWidget(health_group)
        layout.addWidget(controls_group)
        layout.addStretch()
        
        return panel
    
    def create_manual_trading_controls(self) -> QWidget:
        """Create manual trading override controls"""
        group = QGroupBox("⚡ Manual Trading Controls")
        layout = QHBoxLayout(group)
        
        # Symbol selection
        symbol_layout = QVBoxLayout()
        symbol_layout.addWidget(QLabel("Symbol:"))
        self.symbol_combo = QComboBox()
        self.symbol_combo.addItems([
            "BTC/USDT:USDT", "ETH/USDT:USDT", "DOGE/USDT:USDT", 
            "ADA/USDT:USDT", "SOL/USDT:USDT", "MATIC/USDT:USDT"
        ])
        symbol_layout.addWidget(self.symbol_combo)
        
        # Size input
        size_layout = QVBoxLayout()
        size_layout.addWidget(QLabel("Size:"))
        self.size_spinbox = QDoubleSpinBox()
        self.size_spinbox.setRange(0.001, 10000.0)
        self.size_spinbox.setValue(10.0)
        self.size_spinbox.setDecimals(3)
        size_layout.addWidget(self.size_spinbox)
        
        # Price input
        price_layout = QVBoxLayout()
        price_layout.addWidget(QLabel("Price:"))
        self.price_spinbox = QDoubleSpinBox()
        self.price_spinbox.setRange(0.0001, 1000000.0)
        self.price_spinbox.setValue(0.35)
        self.price_spinbox.setDecimals(6)
        price_layout.addWidget(self.price_spinbox)
        
        # Action buttons
        buttons_layout = QVBoxLayout()
        
        # Force Long button
        force_long_btn = QPushButton("📈 Force LONG")
        force_long_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {MatrixTheme.GREEN};
                color: {MatrixTheme.BLACK};
                font-weight: bold;
                padding: 10px;
                border-radius: 5px;
            }}
        """)
        force_long_btn.clicked.connect(lambda: self.request_manual_trade('long'))
        
        # Force Short button
        force_short_btn = QPushButton("📉 Force SHORT")
        force_short_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {MatrixTheme.RED};
                color: {MatrixTheme.WHITE};
                font-weight: bold;
                padding: 10px;
                border-radius: 5px;
            }}
        """)
        force_short_btn.clicked.connect(lambda: self.request_manual_trade('short'))
        
        # Close All button
        close_all_btn = QPushButton("❌ Close All")
        close_all_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {MatrixTheme.YELLOW};
                color: {MatrixTheme.BLACK};
                font-weight: bold;
                padding: 10px;
                border-radius: 5px;
            }}
        """)
        close_all_btn.clicked.connect(lambda: self.request_manual_trade('close_all'))
        
        buttons_layout.addWidget(force_long_btn)
        buttons_layout.addWidget(force_short_btn)
        buttons_layout.addWidget(close_all_btn)
        
        # Add all layouts
        layout.addLayout(symbol_layout)
        layout.addLayout(size_layout)
        layout.addLayout(price_layout)
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        return group
    
    def setup_update_timer(self):
        """Setup timer for regular updates"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_dashboard)
        self.update_timer.start(1000)  # Update every second
    
    def request_manual_trade(self, action: str):
        """Request manual trade execution"""
        trade_params = {
            'action': action,
            'symbol': self.symbol_combo.currentText(),
            'size': self.size_spinbox.value(),
            'price': self.price_spinbox.value(),
            'timestamp': datetime.now()
        }
        
        # Emit signal for manual trade request
        self.manual_trade_requested.emit(trade_params)
        
        print(f"🎯 Manual trade requested: {action} {trade_params['size']} {trade_params['symbol']} @ {trade_params['price']}")
    
    def update_dashboard(self):
        """Update dashboard with latest data"""
        # Update health metrics
        self.update_health_display()
        
        # Update positions table
        self.update_positions_table()
        
        # Update orders table
        self.update_orders_table()
    
    def update_health_display(self):
        """Update system health display"""
        health = self.system_health
        
        # Update health labels
        self.health_labels['connection_status'].setText(health.connection_status)
        self.health_labels['last_update'].setText(health.last_update.strftime("%H:%M:%S"))
        self.health_labels['cpu_usage'].setText(f"{health.cpu_usage:.1f}%")
        self.health_labels['memory_usage'].setText(f"{health.memory_usage:.1f}%")
        self.health_labels['api_latency'].setText(f"{health.api_latency:.0f}ms")
        self.health_labels['error_count'].setText(str(health.error_count))
        self.health_labels['uptime'].setText(str(health.uptime).split('.')[0])
        
        # Color code connection status
        status_color = MatrixTheme.GREEN if health.connection_status == "Connected" else MatrixTheme.RED
        self.health_labels['connection_status'].setStyleSheet(f"color: {status_color}; font-weight: bold;")
    
    def update_positions_table(self):
        """Update positions table"""
        self.positions_table.setRowCount(len(self.positions))
        
        for row, position in enumerate(self.positions):
            self.positions_table.setItem(row, 0, QTableWidgetItem(position.symbol))
            self.positions_table.setItem(row, 1, QTableWidgetItem(position.side.upper()))
            self.positions_table.setItem(row, 2, QTableWidgetItem(f"{position.size:.3f}"))
            self.positions_table.setItem(row, 3, QTableWidgetItem(f"{position.entry_price:.6f}"))
            self.positions_table.setItem(row, 4, QTableWidgetItem(f"{position.current_price:.6f}"))
            self.positions_table.setItem(row, 5, QTableWidgetItem(f"{position.unrealized_pnl:.2f}"))
            self.positions_table.setItem(row, 6, QTableWidgetItem(f"{position.percentage:.2f}%"))
            
            # Color code PnL
            pnl_color = MatrixTheme.GREEN if position.unrealized_pnl >= 0 else MatrixTheme.RED
            self.positions_table.item(row, 5).setForeground(QColor(pnl_color))
            self.positions_table.item(row, 6).setForeground(QColor(pnl_color))
    
    def update_orders_table(self):
        """Update orders table"""
        self.orders_table.setRowCount(len(self.orders))
        
        for row, order in enumerate(self.orders):
            self.orders_table.setItem(row, 0, QTableWidgetItem(order.id[:8]))
            self.orders_table.setItem(row, 1, QTableWidgetItem(order.symbol))
            self.orders_table.setItem(row, 2, QTableWidgetItem(order.side.upper()))
            self.orders_table.setItem(row, 3, QTableWidgetItem(order.type.upper()))
            self.orders_table.setItem(row, 4, QTableWidgetItem(f"{order.amount:.3f}"))
            self.orders_table.setItem(row, 5, QTableWidgetItem(f"{order.price:.6f}"))
            self.orders_table.setItem(row, 6, QTableWidgetItem(order.status.upper()))
    
    def update_positions(self, positions: List[PositionInfo]):
        """Update positions data"""
        self.positions = positions
    
    def update_orders(self, orders: List[OrderInfo]):
        """Update orders data"""
        self.orders = orders
    
    def update_system_health(self, health: SystemHealth):
        """Update system health data"""
        self.system_health = health
