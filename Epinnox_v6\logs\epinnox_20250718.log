2025-07-18 06:10:05,157 - main - INFO - Epinnox v6 starting up...
2025-07-18 06:10:05,203 - core.performance_monitor - INFO - Performance monitoring started
2025-07-18 06:10:05,203 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-18 06:10:05,204 - main - INFO - Performance monitoring initialized
2025-07-18 06:10:05,214 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-18 06:10:05,223 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-18 06:10:05,224 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-18 06:10:11,089 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-18 06:10:14,628 - core.emergency_stop_coordinator - INFO - Emergency Stop Coordinator initialized
2025-07-18 06:10:14,639 - core.emergency_stop_coordinator - INFO - Emergency Stop Coordinator initialized
2025-07-18 06:10:14,640 - core.emergency_stop_coordinator - INFO - [OK] Registered module for emergency stop: main_window
2025-07-18 06:10:15,691 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-18 06:10:17,545 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-18 06:10:18,819 - websocket - INFO - Websocket connected
2025-07-18 06:10:21,026 - trading.position_tracker - INFO - Position Tracker initialized
2025-07-18 06:10:21,926 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-18 06:10:21,927 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-18 06:10:21,927 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-18 06:10:21,927 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-18 06:10:21,941 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-18 06:10:23,996 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-18 06:10:23,997 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-18 06:10:23,997 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-18 06:10:24,032 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-18 06:10:24,033 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-18 06:10:24,033 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-18 06:10:24,033 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-18 06:10:24,034 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-18 06:10:24,037 - core.signal_hierarchy - INFO - Signal Hierarchy initialized
2025-07-18 06:10:24,132 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-18 06:10:24,132 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-18 06:10:24,133 - storage.session_manager - INFO - Session Manager initialized
2025-07-18 06:10:24,137 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250718_061024_8863e929
2025-07-18 06:10:24,137 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250718_061024_8863e929
2025-07-18 06:10:24,299 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-18 06:10:24,310 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-18 06:10:24,311 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-18 06:10:24,311 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-18 06:10:24,311 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-18 06:10:24,311 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-18 06:10:24,314 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-18 06:10:24,325 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-18 06:10:24,326 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-18 06:10:24,327 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Registered timer 'symbol_scanner_update': interval=30.0s, priority=MEDIUM
2025-07-18 06:10:24,327 - core.symbol_scanner - INFO - [SYMBOL_SCANNER] Registered with timer coordinator (interval: 30.0s)
2025-07-18 06:10:24,328 - core.llm_action_executors - WARNING - ⚠️ Intelligent Limit Order Manager not available
2025-07-18 06:10:24,328 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-18 06:11:35,559 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-18 06:12:41,194 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:12:41,197 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:12:41,198 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:12:41,198 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:12:41,198 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:12:41,198 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:12:41,199 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:12:41,198 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:12:41,198 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:12:41,199 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:12:41,199 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:12:41,199 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:12:41,199 - core.llm_orchestrator - INFO - 🚀 Submitted 5 prompts for parallel execution
2025-07-18 06:12:41,200 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:12:41,200 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.240144/$0.240145
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.239102 (Distance: 0.50%)
Resistance: $0.241506
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:12:41,201 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:12:41,201 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.240304 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:12:41,201 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:12:41,201 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:12:41,201 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-18 06:12:41,201 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:12:41,202 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:12:44,972 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 225 chars
2025-07-18 06:12:44,973 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a rapid price increase.",
  "take_profit": 2.5,
  "stop_loss": 1.7,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:12:44,974 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 92, Total: 266
2025-07-18 06:12:44,977 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:12:44,978 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:12:44,979 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:12:44,980 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:12:46,626 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 174 chars
2025-07-18 06:12:46,627 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Momentum neutral but spread favorable"
}...
2025-07-18 06:12:46,627 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 84, Total: 926
2025-07-18 06:12:46,628 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:12:46,629 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:12:46,629 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:12:46,630 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:12:50,108 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 764 chars
2025-07-18 06:12:50,109 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 85%, TAKE_PROFIT: 10%, STOP_LOSS: -7%, EXPLANATION: The DOGE/USDT pair shows a neutral sector momentum with no recent price movement and stable volume. Given the account's healthy status, normal trading parameters are acceptable for this conservative entry setup. A high c...
2025-07-18 06:12:50,109 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 938, Completion: 196, Total: 1134
2025-07-18 06:12:50,110 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:12:50,117 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 10.0, 'STOP_LOSS': -7.0, 'EXPLANATION': "THE DOGE/USDT PAIR SHOWS A NEUTRAL SECTOR MOMENTUM WITH NO RECENT PRICE MOVEMENT AND STABLE VOLUME. GIVEN THE ACCOUNT'S HEALTHY STATUS, NORMAL TRADING PARAMETERS ARE ACCEPTABLE FOR THIS CONSERVATIVE ENTRY SETUP. A HIGH CONFIDENCE LEVEL IN TAKING LONG POSITIONS IS JUSTIFIED DUE TO HISTORICAL STABILITY OF PRICES WITHIN 1-3% VOLATILITY RANGE PREFERRED BY OUR CRITERIA. THE TAKE PROFIT AT A +10% RETURN AND STOP LOSS SET AT -7% HELP MANAGE RISK WHILE AIMING FOR PROFITABLE OUTCOMES, ALIGNING WITH THE ACCOUNT'S HEALTHY STATUS WHICH ALLOWS HIGHER EXPOSURE UP TO $40 (2.0% OF TOTAL BALANCE) PER TRADE WITHOUT EXCEEDING MAX TOTAL EXPOSURE LIMITS OR IMPACT ON ACCOUNT PRESERVATION CRITERIA.", 'ACTION': 'ENTER_NOW'}
2025-07-18 06:12:50,118 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:12:50,118 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-18 06:12:50,119 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:12:51,259 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 138 chars
2025-07-18 06:12:51,259 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "regime": "RANGING_TIGHT",
  "confidence": 75,
  "scalpability": "Medium",
  "recommended_timeframe": "1m",
  "risk_level": "Medium"
}...
2025-07-18 06:12:51,260 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 64, Total: 660
2025-07-18 06:12:51,260 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalpability', 'recommended_timeframe', 'risk_level']
2025-07-18 06:12:51,261 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALPABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:12:51,261 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:12:51,262 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:12:53,031 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 362 chars
2025-07-18 06:12:53,032 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "risk_adjustment": [0.5, 2.0],
  "hold_time_target": ["30s", "5min"],
  "entry_threshold": [70, 60],
  "reasoning": "Maintain current strategy - performing well with a moderate win rate and acceptable drawdown level under normal market volatility. Adjust risk to account for recent poor performan...
2025-07-18 06:12:53,032 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 115, Total: 734
2025-07-18 06:12:53,032 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'reasoning', 'confidence']
2025-07-18 06:12:53,033 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'REASONING', 'CONFIDENCE']
2025-07-18 06:12:53,033 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-18 06:12:53,033 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-18 06:12:53,033 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-18 06:12:53,034 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 11.84s - 5 prompts executed concurrently
2025-07-18 06:12:53,035 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:12:53,035 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:12:53,036 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.5, WAIT: 3.5
2025-07-18 06:12:57,386 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $23.95, Required Margin: $1.20, Account Balance: $43.20
2025-07-18 06:12:57,387 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:12:57,387 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:12:59,676 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:13:10,735 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:13:10,736 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:13:10,736 - core.llm_orchestrator - INFO - 🚀 Submitted 1 prompts for parallel execution
2025-07-18 06:13:10,737 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:13:10,737 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.240009/$0.240010
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.238922 (Distance: 0.50%)
Resistance: $0.241324
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:13:10,737 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:13:14,662 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 203 chars
2025-07-18 06:13:14,662 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Price near support level with favorable spread and no volume spike"
}...
2025-07-18 06:13:14,663 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 89, Total: 931
2025-07-18 06:13:14,663 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:13:14,663 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:13:14,663 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:13:14,663 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:13:14,664 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 3.93s - 1 prompts executed concurrently
2025-07-18 06:13:14,664 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (100.0%) - Decision based on weighted votes: LONG (100.0%)
2025-07-18 06:13:14,664 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (100.0%) - Decision based on weighted votes: LONG (100.0%)
2025-07-18 06:13:14,665 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.8
2025-07-18 06:13:40,783 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:13:40,784 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:13:40,785 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:13:40,785 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:13:40,785 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:13:40,786 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:13:40,786 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:13:40,786 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-18 06:13:40,786 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:13:40,786 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:13:40,786 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:13:40,787 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:13:40,787 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.240179/$0.240180
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.238963 (Distance: 0.50%)
Resistance: $0.241365
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:13:40,789 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:13:44,602 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 203 chars
2025-07-18 06:13:44,602 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 78,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 0,
  "REASONING": "Neutral signal with favorable spread and dynamic key levels present"
}...
2025-07-18 06:13:44,603 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 88, Total: 930
2025-07-18 06:13:44,603 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:13:44,604 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:13:44,604 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:13:44,604 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:13:45,949 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 165 chars
2025-07-18 06:13:45,949 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
    "REGIME": "RANGING_TIGHT",
    "CONFIDENCE": 85,
    "SCALP_SUITABILITY": "MEDIUM",
    "RECOMMENDED_TIMEFRAME": "1m",
    "RISK_LEVEL": "MEDIUM"
}
```...
2025-07-18 06:13:45,949 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 88, Total: 684
2025-07-18 06:13:45,949 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:13:45,950 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:13:45,950 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:13:45,950 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:13:47,329 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 234 chars
2025-07-18 06:13:47,329 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 92,
  "entry_reason": "Market broke through resistance level at $1350 with a rapid move to $1360.",
  "take_profit": 2.75,
  "stop_loss": 1.4,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:13:47,330 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 104, Total: 278
2025-07-18 06:13:47,330 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:13:47,330 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:13:47,330 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:13:47,330 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:13:47,331 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 6.55s - 3 prompts executed concurrently
2025-07-18 06:13:47,332 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:13:47,332 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:13:47,332 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 2.0, WAIT: 3.4
2025-07-18 06:13:51,393 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $24.10, Required Margin: $1.21, Account Balance: $42.86
2025-07-18 06:13:51,393 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:13:51,394 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 100.******** DOGE → 1.******** contracts
2025-07-18 06:13:54,216 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:14:10,856 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:14:10,857 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:14:10,857 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:14:10,857 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:14:10,858 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:14:10,858 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-18 06:14:10,858 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:14:10,858 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:14:10,858 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:14:10,859 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:14:10,859 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.240134/$0.240135
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.238993 (Distance: 0.50%)
Resistance: $0.241395
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:14:10,859 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.240194 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:14:10,860 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:14:10,860 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:14:15,282 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 385 chars
2025-07-18 06:14:15,283 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 85,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "The current regime is classified as 'RANGING_TIGHT' due to the neutral trend strength and medium volatility. This suggests a balanced...
2025-07-18 06:14:15,283 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 146, Total: 742
2025-07-18 06:14:15,283 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 185 (char 184)
2025-07-18 06:14:15,283 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "REGIME": "RANGING_TIGHT", "CONFIDENCE": 85, "SCALP_SUITABILITY": "MEDIUM", "RECOMMENDED_TIMEFRAME": "1m", "RISK_LEVEL": "MEDIUM", "REASONING": "The current regime is classified as "RANGING_TIGHT" d...
2025-07-18 06:14:15,284 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['confidence', 'reasoning']
2025-07-18 06:14:15,284 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-18 06:14:15,285 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:14:15,285 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:14:16,792 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 210 chars
2025-07-18 06:14:16,792 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Low spread and balanced recent flow indicate a favorable entry condition."
}...
2025-07-18 06:14:16,792 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 90, Total: 932
2025-07-18 06:14:16,792 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:14:16,793 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:14:16,793 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:14:16,793 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:14:20,113 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 904 chars
2025-07-18 06:14:20,114 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 85%, TAKE_PROFIT: 3%, STOP_LOSS: -5%, EXPLANATION: The DOGE/USDT pair shows a slight positive momentum with no significant changes in the last five prices and signals. Given that our account health is good, we can afford to take on this opportunity while adhering to conse...
2025-07-18 06:14:20,114 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 938, Completion: 209, Total: 1147
2025-07-18 06:14:20,114 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:14:20,114 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 3.0, 'STOP_LOSS': -5.0, 'EXPLANATION': "THE DOGE/USDT PAIR SHOWS A SLIGHT POSITIVE MOMENTUM WITH NO SIGNIFICANT CHANGES IN THE LAST FIVE PRICES AND SIGNALS. GIVEN THAT OUR ACCOUNT HEALTH IS GOOD, WE CAN AFFORD TO TAKE ON THIS OPPORTUNITY WHILE ADHERING TO CONSERVATIVE TRADING PRINCIPLES SUCH AS MAINTAINING RISK WITHIN 2% OF TOTAL EXPOSURE PER TRADE (WHICH ALIGNS WELL WITH TAKING PROFITS AT A 3% RATE) AND SETTING STOP LOSSES BELOW THE CURRENT PRICE BY 5%. THIS APPROACH ENSURES WE CAPITALIZE ON POTENTIAL GAINS WITHOUT COMPROMISING OUR ACCOUNT'S HEALTH. THE HISTORICAL CONTEXT SHOWS THAT THERE HAS BEEN NO SIGNIFICANT MOVEMENT, SUGGESTING STABILITY IN THIS OPPORTUNITY WHICH FURTHER SUPPORTS PROCEEDING WITH CAUTION BUT NOT HESITANCY TO ENTER A LONG POSITION AT PRESENT LEVELS WITHIN THE CONSERVATIVE PARAMETERS SET BY MARKET OVERVIEW AND PERSONAL RISK CRITERIA.", 'ACTION': 'ENTER_NOW'}
2025-07-18 06:14:20,114 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:14:20,115 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-18 06:14:20,115 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:14:20,116 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 9.26s - 3 prompts executed concurrently
2025-07-18 06:14:20,117 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (90.2%) - Decision based on weighted votes: LONG (90.2%)
2025-07-18 06:14:20,117 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (90.2%) - Decision based on weighted votes: LONG (90.2%)
2025-07-18 06:14:20,117 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.5, WAIT: 0.4
2025-07-18 06:14:40,856 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:14:40,857 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:14:40,858 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:14:40,858 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:14:40,858 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:14:40,858 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:14:40,858 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-18 06:14:40,858 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:14:40,859 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:14:40,859 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:14:40,859 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:14:40,859 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.239964/$0.239965
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.238780 (Distance: 0.50%)
Resistance: $0.241180
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:14:40,859 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:14:40,860 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:14:44,606 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 180 chars
2025-07-18 06:14:44,606 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Price near key levels with favorable spread"
}...
2025-07-18 06:14:44,606 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 841, Completion: 84, Total: 925
2025-07-18 06:14:44,607 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:14:44,607 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:14:44,607 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:14:44,607 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:14:45,873 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 224 chars
2025-07-18 06:14:45,874 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a strong volume spike.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:14:45,875 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 93, Total: 267
2025-07-18 06:14:45,876 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:14:45,876 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:14:45,877 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:14:45,877 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:14:47,978 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 347 chars
2025-07-18 06:14:47,979 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Dynamic regime detection based on volatility and trend analysis indicates a medium suitability for scalping in the current RANGING_TI...
2025-07-18 06:14:47,980 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 133, Total: 729
2025-07-18 06:14:47,981 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-18 06:14:47,981 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-18 06:14:47,982 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:14:47,983 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:14:47,984 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 7.13s - 3 prompts executed concurrently
2025-07-18 06:14:47,986 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:14:47,986 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:14:47,987 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.8, WAIT: 3.3
2025-07-18 06:14:52,019 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $22.56, Required Margin: $1.13, Account Balance: $40.70
2025-07-18 06:14:52,019 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:14:52,020 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:14:54,862 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:15:10,866 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:15:10,866 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:15:10,867 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:15:10,867 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:15:10,867 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:15:10,867 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:15:10,867 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:15:10,868 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.239847/$0.239848
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.238706 (Distance: 0.50%)
Resistance: $0.241106
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:15:10,869 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:15:10,868 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:15:10,868 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-18 06:15:10,868 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:15:10,868 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:15:10,873 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.239906 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:15:10,871 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:15:10,873 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:15:10,874 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-18 06:15:10,874 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:15:14,632 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 333 chars
2025-07-18 06:15:14,633 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"risk_adjustment":1.5, "hold_time_target":6,"entry_threshold":75, "exit_threshold":65, "sizing_method":"VARIABLE_RISK", "reasoning":"Increase risk slightly due to stable market regime confidence and improve entry threshold for better win rate. Adjust hold time shorter as the current average is long...
2025-07-18 06:15:14,633 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 97, Total: 716
2025-07-18 06:15:14,633 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning', 'confidence']
2025-07-18 06:15:14,633 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-18 06:15:14,634 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.5x
2025-07-18 06:15:14,634 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.5x, Hold time 6min
2025-07-18 06:15:14,634 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-18 06:15:15,752 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 138 chars
2025-07-18 06:15:15,752 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "regime": "RANGING_TIGHT",
  "confidence": 75,
  "scalpability": "Medium",
  "recommended_timeframe": "1m",
  "risk_level": "Medium"
}...
2025-07-18 06:15:15,752 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 64, Total: 660
2025-07-18 06:15:15,753 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalpability', 'recommended_timeframe', 'risk_level']
2025-07-18 06:15:15,753 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALPABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:15:15,753 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:15:15,753 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:15:19,720 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1100 chars
2025-07-18 06:15:19,720 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 10%, STOP_LOSS: -7%, EXPLANATION: The current market conditions indicate a neutral sector momentum and normal volatility with an average volume profile. Given the account's healthy status, there is room for conservative trading strategies that align well...
2025-07-18 06:15:19,720 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 938, Completion: 249, Total: 1187
2025-07-18 06:15:19,721 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:15:19,721 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'TAKE_PROFIT': 10.0, 'STOP_LOSS': -7.0, 'EXPLANATION': "THE CURRENT MARKET CONDITIONS INDICATE A NEUTRAL SECTOR MOMENTUM AND NORMAL VOLATILITY WITH AN AVERAGE VOLUME PROFILE. GIVEN THE ACCOUNT'S HEALTHY STATUS, THERE IS ROOM FOR CONSERVATIVE TRADING STRATEGIES THAT ALIGN WELL WITH HISTORICAL DATA TRENDS OF DOGE/USDT WHICH HAVE SHOWN CONSISTENT PERFORMANCE WITHOUT SIGNIFICANT PRICE FLUCTUATIONS RECENTLY. THE SETUP QUALITY APPEARS HIGH AS IT MEETS ALL OPPORTUNITY CRITERIA SUCH AS A CLEAR PATTERN AND STRONG SIGNALS WITHIN THE MARKET'S NORMAL RANGE, THUS JUSTIFYING AN ENTRY INTO THIS POSITION AT CURRENT LEVELS WHILE MAINTAINING STRICT RISK MANAGEMENT LIMITS TO PRESERVE ACCOUNT HEALTH (MAX 2% PER TRADE). A TAKE-PROFIT OF 10% IS SET CONSIDERING POTENTIAL SHORT-TERM GAINS WITHOUT OVEREXPOSURE. THE STOP LOSS LEVEL HAS BEEN DETERMINED CONSERVATIVELY AT -7%, ENSURING THE PROTECTION AGAINST SUDDEN MARKET REVERSALS WHILE STILL ALLOWING FOR A REASONABLE PROFIT MARGIN IN LINE WITH HISTORICAL PERFORMANCE AND RISK/REWARD RATIOS REQUIRED BY HEALTHY ACCOUNT STATUS (>3:1 RATIO).", 'ACTION': 'ENTER_NOW'}
2025-07-18 06:15:19,722 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:15:19,722 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-18 06:15:19,723 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:15:21,195 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 174 chars
2025-07-18 06:15:21,196 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Spread favorable and momentum neutral"
}...
2025-07-18 06:15:21,196 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 83, Total: 925
2025-07-18 06:15:21,196 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:15:21,197 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:15:21,197 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:15:21,197 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:15:21,198 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 10.33s - 4 prompts executed concurrently
2025-07-18 06:15:21,199 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (81.5%) - Decision based on weighted votes: LONG (81.5%)
2025-07-18 06:15:21,199 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (81.5%) - Decision based on weighted votes: LONG (81.5%)
2025-07-18 06:15:21,199 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.4, WAIT: 0.8
2025-07-18 06:15:40,859 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:15:40,860 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:15:40,861 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:15:40,861 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:15:40,861 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:15:40,861 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-18 06:15:40,861 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:15:40,861 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:15:40,862 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.239888/$0.239889
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.238884 (Distance: 0.50%)
Resistance: $0.241284
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:15:40,862 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:15:44,556 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 221 chars
2025-07-18 06:15:44,556 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with strong volume indicators.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:15:44,557 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 92, Total: 266
2025-07-18 06:15:44,557 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:15:44,557 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:15:44,557 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:15:44,557 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:15:46,046 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 205 chars
2025-07-18 06:15:46,046 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Price near key levels with favorable spread and momentum neutrality."
}...
2025-07-18 06:15:46,047 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 88, Total: 930
2025-07-18 06:15:46,047 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:15:46,047 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:15:46,048 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:15:46,048 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:15:46,049 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.19s - 2 prompts executed concurrently
2025-07-18 06:15:46,049 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:15:46,049 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:15:46,050 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.8, WAIT: 2.7
2025-07-18 06:15:50,488 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $21.93, Required Margin: $1.10, Account Balance: $39.56
2025-07-18 06:15:50,489 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:15:50,490 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:15:52,834 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:16:11,355 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:16:11,356 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:16:11,356 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:16:11,356 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:16:11,356 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:16:11,357 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:16:11,357 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-18 06:16:11,357 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:16:11,357 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:16:11,357 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.239997/$0.239998
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.238873 (Distance: 0.50%)
Resistance: $0.241273
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:16:11,357 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:16:11,358 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:16:11,358 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.240073 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:16:11,359 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:16:16,330 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 514 chars
2025-07-18 06:16:16,330 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 85,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "LOW",
  "REASONING": "The current regime is classified as RANGING_TIGHT, indicating a dynamic market with low volatility and neutral trend. The confidence lev...
2025-07-18 06:16:16,330 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 168, Total: 764
2025-07-18 06:16:16,331 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 418 (char 417)
2025-07-18 06:16:16,331 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "REGIME": "RANGING_TIGHT", "CONFIDENCE": 85, "SCALP_SUITABILITY": "MEDIUM", "RECOMMENDED_TIMEFRAME": "1m", "RISK_LEVEL": "LOW", "REASONING": "The current regime is classified as RANGING_TIGHT, indic...
2025-07-18 06:16:16,331 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['confidence', 'reasoning']
2025-07-18 06:16:16,332 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-18 06:16:16,332 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:16:16,332 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:16:20,145 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 991 chars
2025-07-18 06:16:20,146 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 10%, STOP_LOSS: -5%, EXPLANATION: The DOGE/USDT pair shows a neutral sector momentum with an average volume profile and normal overall volatility. Given the healthy account status, we can afford to take on some risk while maintaining our preservation cri...
2025-07-18 06:16:20,146 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 938, Completion: 236, Total: 1174
2025-07-18 06:16:20,147 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:16:20,148 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'TAKE_PROFIT': 10.0, 'STOP_LOSS': -5.0, 'EXPLANATION': 'THE DOGE/USDT PAIR SHOWS A NEUTRAL SECTOR MOMENTUM WITH AN AVERAGE VOLUME PROFILE AND NORMAL OVERALL VOLATILITY. GIVEN THE HEALTHY ACCOUNT STATUS, WE CAN AFFORD TO TAKE ON SOME RISK WHILE MAINTAINING OUR PRESERVATION CRITERIA OF NOT EXCEEDING 2% PER TRADE OR MORE THAN 70% TOTAL EXPOSURE AT ANY GIVEN TIME. THE HISTORICAL CONTEXT SHOWS A CONSISTENT PRICE WITH NO SIGNIFICANT CHANGES IN MOM (MOVING AVERAGE), WHICH SUGGESTS STABILITY AND AN OPPORTUNITY FOR PROFIT WITHOUT EXCESSIVE DOWNSIDE RISK, JUSTIFYING THE DECISION TO GO LONG ON DOGE/USDT AS IT MEETS OUR CONSERVATIVE ENTRY SETUP CRITERIA OF HIGH QUALITY SIGNALS WITHIN ACCEPTABLE VOLATILITY RANGES. THE TAKE-PROFIT IS SET AT 10% ABOVE THE CURRENT PRICE DUE TO ITS HEALTHY ACCOUNT STATUS AND NORMAL TRADING PARAMETERS WHILE ENSURING A STOP LOSS THAT LIMITS POTENTIAL DOWNSIDE RISK, ADHERING CLOSELY TO BOTH OPPORTUNITY CRITERIA AND PRESERVATION GUIDELINES.', 'ACTION': 'ENTER_NOW'}
2025-07-18 06:16:20,149 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:16:20,149 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-18 06:16:20,150 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:16:21,900 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 206 chars
2025-07-18 06:16:21,901 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 68,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 0,
  "REASONING": "Neutral signal with favorable spread and key levels near current price"
}...
2025-07-18 06:16:21,902 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 89, Total: 931
2025-07-18 06:16:21,903 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:16:21,903 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:16:21,904 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:16:21,905 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:16:21,908 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 10.55s - 3 prompts executed concurrently
2025-07-18 06:16:21,912 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (89.8%) - Decision based on weighted votes: LONG (89.8%)
2025-07-18 06:16:21,913 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (89.8%) - Decision based on weighted votes: LONG (89.8%)
2025-07-18 06:16:21,914 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.3, WAIT: 0.4
2025-07-18 06:16:40,959 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:16:40,959 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:16:40,960 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:16:40,960 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:16:40,960 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:16:40,960 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-18 06:16:40,961 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:16:40,961 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:16:40,961 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.239897/$0.239898
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.238830 (Distance: 0.50%)
Resistance: $0.241230
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:16:40,962 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:16:44,813 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 208 chars
2025-07-18 06:16:44,814 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Neutral technical and signal analysis with favorable spread conditions."
}...
2025-07-18 06:16:44,814 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 841, Completion: 88, Total: 929
2025-07-18 06:16:44,814 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:16:44,814 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:16:44,815 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:16:44,815 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:16:46,096 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 209 chars
2025-07-18 06:16:46,096 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with volume spike.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:16:46,096 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 91, Total: 265
2025-07-18 06:16:46,096 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:16:46,097 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:16:46,097 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:16:46,097 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:16:46,098 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.14s - 2 prompts executed concurrently
2025-07-18 06:16:46,099 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:16:46,099 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:16:46,099 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.8, WAIT: 2.7
2025-07-18 06:16:49,984 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $21.23, Required Margin: $1.06, Account Balance: $38.30
2025-07-18 06:16:49,985 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:16:49,986 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:16:52,306 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:17:10,926 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:17:10,927 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:17:10,927 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:17:10,927 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:17:10,927 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:17:10,928 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:17:10,928 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:17:10,928 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:17:10,928 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-18 06:17:10,928 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.239882/$0.239883
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.238851 (Distance: 0.50%)
Resistance: $0.241251
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:17:10,928 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:17:10,929 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:17:10,929 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-18 06:17:10,930 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:17:14,360 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 138 chars
2025-07-18 06:17:14,360 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "regime": "RANGING_TIGHT",
  "confidence": 75,
  "scalpability": "Medium",
  "recommended_timeframe": "1m",
  "risk_level": "Medium"
}...
2025-07-18 06:17:14,360 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 64, Total: 660
2025-07-18 06:17:14,360 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalpability', 'recommended_timeframe', 'risk_level']
2025-07-18 06:17:14,361 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALPABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:17:14,361 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:17:14,361 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:17:16,782 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 489 chars
2025-07-18 06:17:16,782 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "risk_adjustment": [0.5, 2.0],
  "hold_time_target": "8", // in minutes as per current strategy hold time
  "entry_threshold": 70,
  "exit_threshold": 60,
  "sizing_method": "FIXED_RISK",
  "reasoning": "Maintain current strategy - performing well with a win rate of 50.0% and an average profit a...
2025-07-18 06:17:16,782 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 155, Total: 774
2025-07-18 06:17:16,783 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning', 'confidence']
2025-07-18 06:17:16,783 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-18 06:17:16,783 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-18 06:17:16,784 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-18 06:17:16,784 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-18 06:17:18,368 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 209 chars
2025-07-18 06:17:18,368 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 78,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 0,
  "REASONING": "Low spread and balanced recent flow indicate a favorable entry condition."
}...
2025-07-18 06:17:18,368 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 89, Total: 931
2025-07-18 06:17:18,369 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:17:18,369 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:17:18,369 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:17:18,369 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:17:18,370 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 7.44s - 3 prompts executed concurrently
2025-07-18 06:17:18,371 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (72.2%) - Decision based on weighted votes: LONG (72.2%)
2025-07-18 06:17:18,371 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (72.2%) - Decision based on weighted votes: LONG (72.2%)
2025-07-18 06:17:18,371 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 2.0, WAIT: 0.8
2025-07-18 06:17:41,348 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:17:41,349 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:17:41,349 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:17:41,349 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:17:41,350 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:17:41,350 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:17:41,350 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:17:41,350 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:17:41,350 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-18 06:17:41,350 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:17:41,351 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:17:41,351 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:17:41,352 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:17:41,352 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.239973/$0.239974
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.238684 (Distance: 0.50%)
Resistance: $0.241082
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:17:41,352 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:17:41,352 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.239883 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:17:41,352 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:17:41,352 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:17:44,962 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 234 chars
2025-07-18 06:17:44,963 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with high volume and positive news release.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:17:44,964 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 94, Total: 268
2025-07-18 06:17:44,964 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:17:44,965 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:17:44,965 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:17:44,965 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:17:46,348 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 225 chars
2025-07-18 06:17:46,348 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "regime": "RANGING_TIGHT",
  "confidence": 75,
  "scalp_suitability": "MEDIUM",
  "recommended_timeframe": "1m",
  "risk_level": "MEDIUM",
  "reasoning": "Dynamic regime detection based on volatility and trend analysis"
}...
2025-07-18 06:17:46,348 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 88, Total: 684
2025-07-18 06:17:46,349 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalp_suitability', 'recommended_timeframe', 'risk_level', 'reasoning']
2025-07-18 06:17:46,349 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-18 06:17:46,349 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:17:46,349 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:17:48,229 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 397 chars
2025-07-18 06:17:48,229 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 75%, TAKE_PROFIT: 1.2%, STOP_LOSS: -0.6%, EXPLANATION: The historical context shows a consistent price with no significant momentum changes and the account is healthy allowing for normal trading parameters to be acceptable, thus suggesting an entry into DOGE/USDT at curre...
2025-07-18 06:17:48,230 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 938, Completion: 106, Total: 1044
2025-07-18 06:17:48,230 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:17:48,230 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 75.0, 'TAKE_PROFIT': 1.2, 'STOP_LOSS': -0.6, 'EXPLANATION': 'THE HISTORICAL CONTEXT SHOWS A CONSISTENT PRICE WITH NO SIGNIFICANT MOMENTUM CHANGES AND THE ACCOUNT IS HEALTHY ALLOWING FOR NORMAL TRADING PARAMETERS TO BE ACCEPTABLE, THUS SUGGESTING AN ENTRY INTO DOGE/USDT AT CURRENT LEVELS WHILE ADHERING TO CONSERVATIVE RISK MANAGEMENT STRATEGIES IN LINE WITH MARKET OVERVIEW.', 'ACTION': 'ENTER_NOW'}
2025-07-18 06:17:48,230 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:17:48,231 - core.llm_response_parsers - INFO - Opportunity scanner parsed: MOMENTUM (MOMENTUM)
2025-07-18 06:17:48,231 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:17:49,829 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 203 chars
2025-07-18 06:17:49,829 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Price near support level with favorable spread and neutral signals"
}...
2025-07-18 06:17:49,829 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 87, Total: 929
2025-07-18 06:17:49,830 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:17:49,830 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:17:49,830 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:17:49,830 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:17:49,832 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 8.48s - 4 prompts executed concurrently
2025-07-18 06:17:49,832 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:17:49,832 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:17:49,833 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.2, WAIT: 3.3
2025-07-18 06:17:53,702 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $20.59, Required Margin: $1.03, Account Balance: $37.12
2025-07-18 06:17:53,702 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:17:53,703 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:17:56,002 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:18:58,994 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:18:58,995 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:18:58,995 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:18:58,995 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:18:58,995 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:18:58,996 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:18:58,996 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:18:58,996 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:18:58,996 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-18 06:18:58,997 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:18:58,997 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:18:58,998 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:18:58,998 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:18:58,998 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.238949/$0.238950
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.237775 (Distance: 0.50%)
Resistance: $0.240165
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:18:58,998 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.238970 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:18:58,999 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:18:58,999 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:18:58,999 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:19:02,573 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 228 chars
2025-07-18 06:19:02,574 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above the resistance level at $125 with a volume spike.",
  "take_profit": 3.0,
  "stop_loss": 1.5,
  "hold_time": "3 minutes",
  "leverage": 40
}
```...
2025-07-18 06:19:02,574 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 98, Total: 272
2025-07-18 06:19:02,574 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:19:02,574 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:19:02,575 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:19:02,575 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:19:04,954 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 478 chars
2025-07-18 06:19:04,955 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 85,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "The RANGING_TIGHT regime is characterized by a neutral trend with medium volatility and normal volume levels. This suggests that whil...
2025-07-18 06:19:04,956 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 161, Total: 757
2025-07-18 06:19:04,956 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 293 (char 292)
2025-07-18 06:19:04,957 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "REGIME": "RANGING_TIGHT", "CONFIDENCE": 85, "SCALP_SUITABILITY": "MEDIUM", "RECOMMENDED_TIMEFRAME": "1m", "RISK_LEVEL": "MEDIUM", "REASONING": "The RANGING_TIGHT regime is characterized by a neutra...
2025-07-18 06:19:04,958 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['confidence', 'reasoning']
2025-07-18 06:19:04,958 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-18 06:19:04,958 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:19:04,959 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:19:06,491 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 220 chars
2025-07-18 06:19:06,492 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 65,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 0,
  "REASONING": "Neutral signals with favorable spread and no immediate volume confirmation required."
}...
2025-07-18 06:19:06,494 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 841, Completion: 90, Total: 931
2025-07-18 06:19:06,503 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:19:06,504 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:19:06,504 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:19:06,504 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:19:09,207 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 657 chars
2025-07-18 06:19:09,207 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 85%, TAKE_PROFIT: 10%, STOP_LOSS: -15%, EXPLANATION: The DOGE/USDT pair shows a small positive mom (moving average) with normal volatility and setup quality. Given the account's healthy status, we can take on moderate risk while maintaining an aggressive yet conservative ...
2025-07-18 06:19:09,208 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 937, Completion: 164, Total: 1101
2025-07-18 06:19:09,208 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:19:09,208 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 10.0, 'STOP_LOSS': -15.0, 'EXPLANATION': "THE DOGE/USDT PAIR SHOWS A SMALL POSITIVE MOM (MOVING AVERAGE) WITH NORMAL VOLATILITY AND SETUP QUALITY. GIVEN THE ACCOUNT'S HEALTHY STATUS, WE CAN TAKE ON MODERATE RISK WHILE MAINTAINING AN AGGRESSIVE YET CONSERVATIVE APPROACH TO PROFIT POTENTIAL WITHIN ACCEPTABLE PARAMETERS FOR THIS PARTICULAR MARKET CONDITION. THE HISTORICAL CONTEXT INDICATES THAT THERE HAS BEEN NO CLEAR BULLISH SIGNAL IN RECENT TIMES; HOWEVER, WITH A SLIGHT POSITIVE MOM AND HIGH LIQUIDITY (AS INDICATED BY THE 1X VOLUME), IT PRESENTS AS A REASONABLE OPPORTUNITY UNDER CURRENT HEALTH-ADJUSTED CRITERIA.", 'ACTION': 'ENTER_NOW'}
2025-07-18 06:19:09,208 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:19:09,208 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-18 06:19:09,209 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:19:09,209 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 10.22s - 4 prompts executed concurrently
2025-07-18 06:19:09,210 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (52.0%) - Decision based on weighted votes: LONG (52.0%)
2025-07-18 06:19:09,211 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (52.0%) - Decision based on weighted votes: LONG (52.0%)
2025-07-18 06:19:09,211 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.3, WAIT: 3.1
2025-07-18 06:19:13,164 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $19.62, Required Margin: $0.98, Account Balance: $35.32
2025-07-18 06:19:13,164 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:19:13,165 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:19:15,487 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:19:59,009 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:19:59,010 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:19:59,011 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:19:59,011 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:19:59,011 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:19:59,011 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:19:59,011 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:19:59,011 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:19:59,013 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:19:59,013 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:19:59,012 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:19:59,013 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.239118/$0.239119
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.237881 (Distance: 0.50%)
Resistance: $0.240271
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:19:59,013 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:19:59,013 - core.llm_orchestrator - INFO - 🚀 Submitted 5 prompts for parallel execution
2025-07-18 06:19:59,013 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:19:59,013 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:19:59,013 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:19:59,014 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:19:59,014 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.239076 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:19:59,016 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:19:59,015 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-18 06:19:59,016 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:20:02,710 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 227 chars
2025-07-18 06:20:02,711 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with strong volume indication.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:20:02,711 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 92, Total: 266
2025-07-18 06:20:02,711 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:20:02,711 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:20:02,711 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:20:02,712 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:20:04,067 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 155 chars
2025-07-18 06:20:04,067 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM"
}
```...
2025-07-18 06:20:04,067 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 88, Total: 684
2025-07-18 06:20:04,068 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:20:04,068 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:20:04,068 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:20:04,068 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:20:05,727 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 423 chars
2025-07-18 06:20:05,728 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"risk_adjustment":1.5,"hold_time_target":45,"entry_threshold":65,"exit_threshold":70,"sizing_method":"VARIABLE_RISK","reasoning":"Optimize for current market regime with a slight risk increase to capitalize on potential opportunities, while maintaining confidence in the strategy's effectiveness and...
2025-07-18 06:20:05,728 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 110, Total: 729
2025-07-18 06:20:05,728 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 282 (char 281)
2025-07-18 06:20:05,728 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: {"risk_adjustment":1.5,"hold_time_target":45,"entry_threshold":65,"exit_threshold":70,"sizing_method":"VARIABLE_RISK","reasoning":"Optimize for current market regime with a slight risk increase to cap...
2025-07-18 06:20:05,729 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['reasoning']
2025-07-18 06:20:05,729 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-18 06:20:05,729 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-18 06:20:05,729 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-18 06:20:05,729 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-18 06:20:11,612 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1637 chars
2025-07-18 06:20:11,612 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 15%, STOP_LOSS: -10%, EXPLANATION: The market data indicates a neutral sector momentum with normal volatility and average volume profile. This suggests that the current conditions are stable enough for trading without excessive risk, aligning well with o...
2025-07-18 06:20:11,613 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 938, Completion: 383, Total: 1321
2025-07-18 06:20:11,613 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:20:11,613 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'TAKE_PROFIT': 15.0, 'STOP_LOSS': -10.0, 'EXPLANATION': "THE MARKET DATA INDICATES A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE. THIS SUGGESTS THAT THE CURRENT CONDITIONS ARE STABLE ENOUGH FOR TRADING WITHOUT EXCESSIVE RISK, ALIGNING WELL WITH OUR CONSERVATIVE APPROACH DUE TO ACCOUNT HEALTH STATUS BEING 'HEALTHY'.", 'ACTION': 'ENTER_NOW'}
2025-07-18 06:20:11,614 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:20:11,614 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-18 06:20:11,614 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:20:13,092 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 179 chars
2025-07-18 06:20:13,092 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 80,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 0,
  "REASONING": "Price near key levels with favorable spread"
}...
2025-07-18 06:20:13,093 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 83, Total: 925
2025-07-18 06:20:13,093 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:20:13,093 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:20:13,094 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:20:13,094 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:20:13,095 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 14.09s - 5 prompts executed concurrently
2025-07-18 06:20:13,095 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (51.5%) - Decision based on weighted votes: LONG (51.5%)
2025-07-18 06:20:13,095 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (51.5%) - Decision based on weighted votes: LONG (51.5%)
2025-07-18 06:20:13,096 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.6, WAIT: 3.4
2025-07-18 06:20:17,015 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $18.94, Required Margin: $0.95, Account Balance: $34.27
2025-07-18 06:20:17,015 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:20:17,016 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:20:19,340 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:20:58,970 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:20:58,972 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:20:58,972 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:20:58,972 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:20:58,972 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:20:58,973 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:20:58,973 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:20:58,973 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:20:58,973 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:20:58,973 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-18 06:20:58,975 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:20:58,974 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:20:58,975 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.238430/$0.238431
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.237270 (Distance: 0.50%)
Resistance: $0.239654
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:20:58,977 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:21:02,576 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 155 chars
2025-07-18 06:21:02,577 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM"
}
```...
2025-07-18 06:21:02,577 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 88, Total: 684
2025-07-18 06:21:02,577 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:21:02,577 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:21:02,578 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:21:02,578 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:21:03,828 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 227 chars
2025-07-18 06:21:03,828 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a sudden spike in volume.",
  "take_profit": 2.5,
  "stop_loss": 1.7,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:21:03,829 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 94, Total: 268
2025-07-18 06:21:03,829 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:21:03,829 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:21:03,829 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:21:03,830 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:21:05,265 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 174 chars
2025-07-18 06:21:05,265 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Momentum neutral but spread favorable"
}...
2025-07-18 06:21:05,265 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 84, Total: 926
2025-07-18 06:21:05,266 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:21:05,266 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:21:05,266 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:21:05,266 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:21:05,267 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 6.30s - 3 prompts executed concurrently
2025-07-18 06:21:05,267 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:21:05,268 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:21:05,268 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.8, WAIT: 3.3
2025-07-18 06:21:08,692 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $18.02, Required Margin: $0.90, Account Balance: $32.44
2025-07-18 06:21:08,692 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:21:08,693 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:21:10,969 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:21:58,984 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:21:58,985 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:21:58,986 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:21:58,986 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:21:58,986 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:21:58,986 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:21:58,986 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:21:58,986 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-18 06:21:58,986 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:21:58,987 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:21:58,987 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:21:58,987 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:21:58,987 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:21:58,987 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:21:58,988 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.238126/$0.238127
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.236893 (Distance: 0.50%)
Resistance: $0.239273
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:21:58,988 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.238083 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:21:58,989 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:21:58,990 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:22:02,882 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 209 chars
2025-07-18 06:22:02,883 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Price near key support level with favorable spread and risk/reward ratio"
}...
2025-07-18 06:22:02,883 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 91, Total: 933
2025-07-18 06:22:02,883 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:22:02,883 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:22:02,883 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:22:02,884 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:22:04,144 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 237 chars
2025-07-18 06:22:04,144 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a sudden spike in volume and price.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:22:04,144 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 96, Total: 270
2025-07-18 06:22:04,145 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:22:04,145 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:22:04,145 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:22:04,145 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:22:05,292 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 155 chars
2025-07-18 06:22:05,292 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "regime": "RANGING_TIGHT",
  "confidence": 85,
  "scalp_suitability": "MEDIUM",
  "recommended_timeframe": "1m",
  "risk_level": "MEDIUM"
}
```...
2025-07-18 06:22:05,292 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 73, Total: 669
2025-07-18 06:22:05,293 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalp_suitability', 'recommended_timeframe', 'risk_level']
2025-07-18 06:22:05,293 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:22:05,293 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:22:05,293 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:22:09,302 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1122 chars
2025-07-18 06:22:09,302 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 75%, TAKE_PROFIT: 10%, STOP_LOSS: -4%, EXPLANATION: The DOGE/USDT pair shows a neutral sector momentum with no significant price movement in the last five signals. However, its setup quality is high due to clear patterns and strong signals despite recent mom being flat at...
2025-07-18 06:22:09,302 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 938, Completion: 254, Total: 1192
2025-07-18 06:22:09,303 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:22:09,303 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 75.0, 'TAKE_PROFIT': 10.0, 'STOP_LOSS': -4.0, 'EXPLANATION': 'THE DOGE/USDT PAIR SHOWS A NEUTRAL SECTOR MOMENTUM WITH NO SIGNIFICANT PRICE MOVEMENT IN THE LAST FIVE SIGNALS. HOWEVER, ITS SETUP QUALITY IS HIGH DUE TO CLEAR PATTERNS AND STRONG SIGNALS DESPITE RECENT MOM BEING FLAT AT +0.0%. GIVEN THAT OUR ACCOUNT HEALTH ALLOWS FOR NORMAL TRADING PARAMETERS AND WE HAVE AN EXCELLENT LIQUIDITY SPREAD OF LESS THAN 0.2%, A CONSERVATIVE TRADE WITH HIGHER RISK/REWARD RATIO (1:3) ALIGNS WELL WITHIN THE OPPORTUNITY CRITERIA, CONSIDERING OUR CURRENT MARKET VOLATILITY PREFERENCE IS MODERATE TO LOW AT AROUND 1-3%. THE ACCOUNT IMPACT REMAINS MINIMAL AS WE ARE NOT EXCEEDING HEALTH-BASED LIMITS AND CAN MAINTAIN THREE CONCURRENT POSITIONS. A TAKE PROFIT OF 10% WOULD BE SET BASED ON A CONSERVATIVE ESTIMATE GIVEN NORMAL TRADING CONDITIONS, WHILE THE STOP LOSS WILL PROTECT OUR POSITION IF MARKET VOLATILITY INCREASES UNEXPECTEDLY OR AGAINST STRONG MOMENTUM SHIFTS IN EITHER DIRECTION TO LIMIT POTENTIAL LOSSES WITHIN ACCEPTABLE RISK PARAMETERS FOR AN ACCOUNT WITH HEALTHY STATUS AND MODERATE OVERALL ENVIRONMENT.', 'ACTION': 'ENTER_NOW'}
2025-07-18 06:22:09,303 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:22:09,304 - core.llm_response_parsers - INFO - Opportunity scanner parsed: MOMENTUM (MOMENTUM)
2025-07-18 06:22:09,304 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:22:09,306 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 10.32s - 4 prompts executed concurrently
2025-07-18 06:22:09,307 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:22:09,307 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:22:09,307 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.2, WAIT: 3.3
2025-07-18 06:22:13,596 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $17.18, Required Margin: $0.86, Account Balance: $30.99
2025-07-18 06:22:13,597 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:22:13,598 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:22:15,916 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:22:59,024 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:22:59,025 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:22:59,025 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:22:59,025 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:22:59,025 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:22:59,026 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:22:59,026 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:22:59,026 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:22:59,026 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:22:59,027 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:22:59,027 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:22:59,027 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:22:59,027 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:22:59,027 - core.llm_orchestrator - INFO - 🚀 Submitted 5 prompts for parallel execution
2025-07-18 06:22:59,027 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.238046/$0.238047
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.236815 (Distance: 0.50%)
Resistance: $0.239195
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:22:59,028 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:22:59,028 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:22:59,029 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.238005 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:22:59,029 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:22:59,029 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-18 06:22:59,029 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:22:59,030 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:23:03,365 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 363 chars
2025-07-18 06:23:03,365 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "risk_adjustment": [0.5, 2.0],
  "hold_time_target": ["8", "minutes"],
  "entry_threshold": [70, "%"],
  "exit_threshold": [60, "%"],
  "sizing_method": "FIXED_RISK",
  "reasoning": "Maintain current strategy - performing well with a moderate win rate and acceptable drawdown level under ...
2025-07-18 06:23:03,366 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 131, Total: 750
2025-07-18 06:23:03,366 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning', 'confidence']
2025-07-18 06:23:03,366 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-18 06:23:03,366 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-18 06:23:03,366 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-18 06:23:03,367 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-18 06:23:07,724 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1164 chars
2025-07-18 06:23:07,724 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 75%, TAKE_PROFIT: 1.6%, STOP_LOSS: -4%, EXPLANATION: The current market data indicates a neutral sector momentum with normal volatility and average volume profile which aligns well within the conservative trading parameters of this account's health-adjusted criteria. Give...
2025-07-18 06:23:07,724 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 938, Completion: 281, Total: 1219
2025-07-18 06:23:07,724 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:23:07,725 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 75.0, 'TAKE_PROFIT': 1.6, 'STOP_LOSS': -4.0, 'EXPLANATION': "THE CURRENT MARKET DATA INDICATES A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE WHICH ALIGNS WELL WITHIN THE CONSERVATIVE TRADING PARAMETERS OF THIS ACCOUNT'S HEALTH-ADJUSTED CRITERIA. GIVEN THAT ALL SYMBOLS HAVE POSITIVE MOM (MOVEMENT) AT +0.0% BUT NO SIGNIFICANT CHANGE, IT SUGGESTS STABILITY IN PRICE ACTION RATHER THAN A CLEAR UPWARD OR DOWNWARD TREND FOR IMMEDIATE ENTRY POINTS. HOWEVER, DOGE/USDT HAS BEEN IDENTIFIED AS THE BEST OPPORTUNITY DUE TO ITS MEDIUM SETUP QUALITY AND ALIGNMENT WITH ALL OTHER CRITERIA INCLUDING LIQUIDITY REQUIREMENTS (ASSUMING <0.2% SPREAD), MOMENTUM INDICATORS, AND RISK-REWARD RATIO OF 1:3 WHICH IS HIGHER THAN USUAL FOR A HEALTHY ACCOUNT BUT STILL WITHIN ACCEPTABLE LIMITS CONSIDERING OVERALL MARKET CONDITIONS. THE RECOMMENDED TAKE_PROFIT AT 1.6% WOULD CAPITALIZE ON POTENTIAL GAINS WHILE THE STOP LOSS SET TO -4% ENSURES THAT ANY ADVERSE MOVEMENT WILL LIMIT LOSSES, THUS PRESERVING THE ACCOUNT'S INTEGRITY AS PER ITS SURVIVAL-OVER-PROFIT CRITERIA AND MAINTAINING A HEALTHY RISK BUDGET OF NOT EXCEEDING 2% PER TRADE.", 'ACTION': 'ENTER_NOW'}
2025-07-18 06:23:07,725 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:23:07,725 - core.llm_response_parsers - INFO - Opportunity scanner parsed: MOMENTUM (MOMENTUM)
2025-07-18 06:23:07,725 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:23:08,903 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 155 chars
2025-07-18 06:23:08,903 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "regime": "RANGING_TIGHT",
  "confidence": 75,
  "scalp_suitability": "MEDIUM",
  "recommended_timeframe": "1m",
  "risk_level": "Medium"
}
```...
2025-07-18 06:23:08,904 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 72, Total: 668
2025-07-18 06:23:08,904 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalp_suitability', 'recommended_timeframe', 'risk_level']
2025-07-18 06:23:08,904 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:23:08,904 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:23:08,904 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:23:10,321 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 180 chars
2025-07-18 06:23:10,321 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Price near key levels with favorable spread"
}...
2025-07-18 06:23:10,321 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 84, Total: 926
2025-07-18 06:23:10,322 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:23:10,322 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:23:10,322 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:23:10,322 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:23:11,668 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 259 chars
2025-07-18 06:23:11,668 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a strong candlestick pattern indicating bullish momentum.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:23:11,668 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 100, Total: 274
2025-07-18 06:23:11,669 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:23:11,669 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:23:11,669 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:23:11,669 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:23:11,670 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 12.65s - 5 prompts executed concurrently
2025-07-18 06:23:11,671 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:23:11,671 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:23:11,671 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.2, WAIT: 3.5
2025-07-18 06:23:59,054 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:23:59,055 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:23:59,056 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:23:59,056 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:23:59,056 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:23:59,057 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:23:59,057 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:23:59,057 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:23:59,057 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:23:59,058 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-18 06:23:59,058 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:23:59,058 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:23:59,058 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.237813/$0.237814
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.236692 (Distance: 0.50%)
Resistance: $0.239070
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:23:59,059 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:23:59,060 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:23:59,060 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:23:59,060 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.237881 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:23:59,061 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:24:02,810 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 155 chars
2025-07-18 06:24:02,810 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM"
}
```...
2025-07-18 06:24:02,811 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 88, Total: 684
2025-07-18 06:24:02,811 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:24:02,811 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:24:02,811 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:24:02,812 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:24:06,295 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 864 chars
2025-07-18 06:24:06,296 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 60%, STOP_LOSS: -40%, EXPLANATION: Given the account's healthy status and normal trading parameters, along with a conservative market environment that aligns well with our opportunity criteria (high setup quality), we see an attractive entry point in DOG...
2025-07-18 06:24:06,296 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 938, Completion: 206, Total: 1144
2025-07-18 06:24:06,297 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:24:06,297 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'TAKE_PROFIT': 60.0, 'STOP_LOSS': -40.0, 'EXPLANATION': "GIVEN THE ACCOUNT'S HEALTHY STATUS AND NORMAL TRADING PARAMETERS, ALONG WITH A CONSERVATIVE MARKET ENVIRONMENT THAT ALIGNS WELL WITH OUR OPPORTUNITY CRITERIA (HIGH SETUP QUALITY), WE SEE AN ATTRACTIVE ENTRY POINT IN DOGE/USDT. THE HISTORICAL CONTEXT SHOWS NO RECENT PRICE OR SIGNAL CHANGES SUGGESTING VOLATILITY; HENCE IT FITS THE PREFERRED LOW TO MODERATE RISK RANGE AND LIQUIDITY CONDITIONS, MAINTAINING A HEALTH-ADJUSTED APPROACH WITHOUT EXCEEDING ACCOUNT IMPACT LIMITS. A TAKE PROFIT AT 60% ABOVE ENTRY ENSURES WE CAPITALIZE ON POTENTIAL GAINS WHILE SETTING A STOP LOSS AT -40% PROVIDES ADEQUATE PROTECTION AGAINST SUDDEN MARKET SHIFTS WITHIN THE CONSERVATIVE VOLATILITY RANGE OF OUR ANALYSIS, ALLOWING FOR BOTH GROWTH AND SAFETY IN LINE WITH AN INTELLIGENT OPPORTUNITY STRATEGY.", 'ACTION': 'ENTER_NOW'}
2025-07-18 06:24:06,298 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:24:06,298 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-18 06:24:06,298 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:24:07,675 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 236 chars
2025-07-18 06:24:07,676 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with volume spike indicating strong momentum.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:24:07,676 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 94, Total: 268
2025-07-18 06:24:07,676 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:24:07,677 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:24:07,677 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:24:07,677 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:24:09,252 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 204 chars
2025-07-18 06:24:09,253 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 60,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Price near support level with favorable spread and neutral momentum"
}...
2025-07-18 06:24:09,254 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 87, Total: 929
2025-07-18 06:24:09,255 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:24:09,255 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:24:09,256 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:24:09,256 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:24:09,260 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 10.21s - 4 prompts executed concurrently
2025-07-18 06:24:09,262 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:24:09,263 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:24:09,263 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.1, WAIT: 3.3
2025-07-18 06:27:07,910 - main - INFO - Epinnox v6 starting up...
2025-07-18 06:27:07,925 - core.performance_monitor - INFO - Performance monitoring started
2025-07-18 06:27:07,926 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-18 06:27:07,926 - main - INFO - Performance monitoring initialized
2025-07-18 06:27:07,937 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-18 06:27:07,938 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-18 06:27:07,938 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-18 06:27:11,658 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-18 06:27:15,213 - core.emergency_stop_coordinator - INFO - Emergency Stop Coordinator initialized
2025-07-18 06:27:15,214 - core.emergency_stop_coordinator - INFO - Emergency Stop Coordinator initialized
2025-07-18 06:27:15,214 - core.emergency_stop_coordinator - INFO - [OK] Registered module for emergency stop: main_window
2025-07-18 06:27:15,665 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-18 06:27:16,505 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-18 06:27:17,756 - websocket - INFO - Websocket connected
2025-07-18 06:27:20,694 - trading.position_tracker - INFO - Position Tracker initialized
2025-07-18 06:27:21,077 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-18 06:27:21,077 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-18 06:27:21,077 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-18 06:27:21,078 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-18 06:27:21,083 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-18 06:27:23,121 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-18 06:27:23,121 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-18 06:27:23,122 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-18 06:27:23,124 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-18 06:27:23,124 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-18 06:27:23,125 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-18 06:27:23,125 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-18 06:27:23,126 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-18 06:27:23,128 - core.signal_hierarchy - INFO - Signal Hierarchy initialized
2025-07-18 06:27:23,154 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-18 06:27:23,158 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-18 06:27:23,159 - storage.session_manager - INFO - Session Manager initialized
2025-07-18 06:27:23,165 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250718_062723_81aa73b6
2025-07-18 06:27:23,165 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250718_062723_81aa73b6
2025-07-18 06:27:23,309 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-18 06:27:23,311 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-18 06:27:23,311 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-18 06:27:23,311 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-18 06:27:23,311 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-18 06:27:23,311 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-18 06:27:23,313 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-18 06:27:23,316 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-18 06:27:23,317 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-18 06:27:23,317 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Registered timer 'symbol_scanner_update': interval=30.0s, priority=MEDIUM
2025-07-18 06:27:23,317 - core.symbol_scanner - INFO - [SYMBOL_SCANNER] Registered with timer coordinator (interval: 30.0s)
2025-07-18 06:27:23,317 - core.llm_action_executors - WARNING - ⚠️ Intelligent Limit Order Manager not available
2025-07-18 06:27:23,318 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-18 06:28:29,958 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-18 06:29:05,968 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:29:05,971 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:29:05,972 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:29:05,972 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:29:05,972 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:29:05,973 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:29:05,973 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:29:05,974 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:29:05,975 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:29:05,976 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:29:05,975 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:29:05,978 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:29:05,978 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.238705/$0.238706
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.237521 (Distance: 0.50%)
Resistance: $0.239909
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:29:05,978 - core.llm_orchestrator - INFO - 🚀 Submitted 5 prompts for parallel execution
2025-07-18 06:29:05,978 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:29:05,978 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:29:05,980 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:29:05,981 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:29:05,981 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.238715 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:29:05,983 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-18 06:29:05,984 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:29:05,984 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:29:09,627 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 232 chars
2025-07-18 06:29:09,628 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level on major index futures with volume spike.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:29:09,628 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 96, Total: 270
2025-07-18 06:29:09,629 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:29:09,630 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:29:09,630 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:29:09,631 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:29:10,834 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 165 chars
2025-07-18 06:29:10,834 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
    "regime": "RANGING_TIGHT",
    "confidence": 75,
    "scalp_suitability": "MEDIUM",
    "recommended_timeframe": "1m",
    "risk_level": "MEDIUM"
}
```...
2025-07-18 06:29:10,835 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 73, Total: 669
2025-07-18 06:29:10,835 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalp_suitability', 'recommended_timeframe', 'risk_level']
2025-07-18 06:29:10,836 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:29:10,836 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:29:10,836 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:29:14,566 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 963 chars
2025-07-18 06:29:14,567 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 85%, TAKE_PROFIT: 60%, STOP_LOSS: -40%, EXPLANATION: The current market data shows a neutral sector momentum with normal volatility and average volume profile. Given the healthy account status allowing for trading, there is an opportunity to capitalize on DOGE/USDT due to...
2025-07-18 06:29:14,567 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 938, Completion: 227, Total: 1165
2025-07-18 06:29:14,568 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:29:14,572 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 60.0, 'STOP_LOSS': -40.0, 'EXPLANATION': 'THE CURRENT MARKET DATA SHOWS A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE. GIVEN THE HEALTHY ACCOUNT STATUS ALLOWING FOR TRADING, THERE IS AN OPPORTUNITY TO CAPITALIZE ON DOGE/USDT DUE TO ITS MEDIUM SETUP QUALITY WHICH ALIGNS WELL WITH OUR CONSERVATIVE APPROACH WHILE MAINTAINING RISK WITHIN ACCEPTABLE LIMITS SET BY THE ACCOUNT PRESERVATION CRITERIA (2% PER TRADE MAX). THE HISTORICAL CONTEXT OF A CONSISTENT PRICE AND SIGNALS SUGGEST STABILITY IN THIS POSITION. A TAKE-PROFIT AT 60% ABOVE ENTRY POINT IS REASONABLE TO SECURE GAINS, CONSIDERING MARKET VOLATILITY REMAINS CONSERVATIVE AS REQUIRED FOR OUR STRATEGY PROFILE, WHILE SETTING STOP LOSS LOWER BY 40% WILL HELP PROTECT AGAINST SUDDEN ADVERSE MOVEMENTS WITHOUT COMPROMISING THE OVERALL RISK BUDGET OF HEALTHY ACCOUNT STATUS.', 'ACTION': 'ENTER_NOW'}
2025-07-18 06:29:14,573 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:29:14,573 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-18 06:29:14,574 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:29:16,111 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 193 chars
2025-07-18 06:29:16,111 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 65,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 0,
  "REASONING": "Neutral signals with favorable spread and no volume spike"
}...
2025-07-18 06:29:16,111 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 88, Total: 930
2025-07-18 06:29:16,112 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:29:16,112 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:29:16,112 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:29:16,112 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:29:21,595 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1601 chars
2025-07-18 06:29:21,595 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "risk_adjustment": 1.0, // Adjusted risk per trade to maintain current performance levels within the specified range (0.5-2.0).
  "hold_time_target": 8, // Maintained average hold time at 8 minutes as it is currently performing well and aligns with scalping strategy requirements for quick entrie...
2025-07-18 06:29:21,595 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 385, Total: 1004
2025-07-18 06:29:21,596 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning', 'confidence']
2025-07-18 06:29:21,596 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-18 06:29:21,596 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-18 06:29:21,596 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-18 06:29:21,597 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-18 06:29:21,599 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 15.63s - 5 prompts executed concurrently
2025-07-18 06:29:21,600 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:29:21,601 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:29:21,601 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.3, WAIT: 3.5
2025-07-18 06:29:26,945 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $16.90, Required Margin: $0.85, Account Balance: $30.49
2025-07-18 06:29:26,946 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:29:26,947 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:29:29,267 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:29:36,383 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:29:36,384 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:29:36,384 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:29:36,385 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:29:36,385 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-18 06:29:36,386 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:29:36,386 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:29:36,387 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:29:36,387 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.238831/$0.238832
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.237506 (Distance: 0.50%)
Resistance: $0.239893
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:29:36,389 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:29:40,082 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 237 chars
2025-07-18 06:29:40,082 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "regime": "RANGING_TIGHT",
  "confidence": 75,
  "scalp_suitability": "MEDIUM",
  "recommended_timeframe": "1m",
  "risk_level": "MEDIUM",
  "reasoning": "Dynamic regime detection based on volatility and trend analysis"
}
```...
2025-07-18 06:29:40,082 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 93, Total: 689
2025-07-18 06:29:40,082 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalp_suitability', 'recommended_timeframe', 'risk_level', 'reasoning']
2025-07-18 06:29:40,083 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-18 06:29:40,083 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:29:40,083 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:29:41,513 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 183 chars
2025-07-18 06:29:41,513 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 60,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 0,
  "REASONING": "Price near support/resistance; favorable spread"
}...
2025-07-18 06:29:41,513 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 840, Completion: 85, Total: 925
2025-07-18 06:29:41,514 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:29:41,514 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:29:41,514 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:29:41,514 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:29:41,515 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.13s - 2 prompts executed concurrently
2025-07-18 06:29:41,516 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (72.7%) - Decision based on weighted votes: LONG (72.7%)
2025-07-18 06:29:41,516 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (72.7%) - Decision based on weighted votes: LONG (72.7%)
2025-07-18 06:29:41,516 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.5, WAIT: 0.6
2025-07-18 06:30:05,977 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:30:05,978 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:30:05,979 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:30:05,979 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:30:05,979 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:30:05,980 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:30:05,980 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-18 06:30:05,980 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:30:05,980 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:30:05,985 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.238835/$0.238836
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.237519 (Distance: 0.50%)
Resistance: $0.239907
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:30:05,985 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:30:05,986 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:30:05,986 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.238713 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:30:05,986 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:30:09,612 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 232 chars
2025-07-18 06:30:09,613 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance at $135 with a sudden spike to $140.",
  "take_profit": 2.5,
  "stop_loss": 1.7,
  "hold_time": "3 minutes",
  "leverage": 40
}
```...
2025-07-18 06:30:09,613 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 101, Total: 275
2025-07-18 06:30:09,613 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:30:09,614 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:30:09,614 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:30:09,614 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:30:11,077 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 186 chars
2025-07-18 06:30:11,077 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 60,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Neutral signals but favorable spread and low risk"
}...
2025-07-18 06:30:11,078 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 87, Total: 929
2025-07-18 06:30:11,078 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:30:11,078 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:30:11,078 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:30:11,079 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:30:15,302 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1094 chars
2025-07-18 06:30:15,303 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 85%, TAKE_PROFIT: 60%, STOP_LOSS: 40%, EXPLANATION: The current market conditions show a neutral sector momentum and normal volatility with an average volume profile. This suggests that the markets are stable, which is conducive to conservative trading strategies. Given t...
2025-07-18 06:30:15,303 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 938, Completion: 272, Total: 1210
2025-07-18 06:30:15,303 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:30:15,304 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 60.0, 'STOP_LOSS': 40.0, 'EXPLANATION': "THE CURRENT MARKET CONDITIONS SHOW A NEUTRAL SECTOR MOMENTUM AND NORMAL VOLATILITY WITH AN AVERAGE VOLUME PROFILE. THIS SUGGESTS THAT THE MARKETS ARE STABLE, WHICH IS CONDUCIVE TO CONSERVATIVE TRADING STRATEGIES. GIVEN THE ACCOUNT'S HEALTHY STATUS AND ADHERENCE TO PRESERVATION CRITERIA (MAX RISK PER TRADE AT 2% OF TOTAL BALANCE), IT ALLOWS FOR A HIGHER CONFIDENCE LEVEL IN TAKING POSITIONS WITHOUT EXCESSIVE EXPOSURE. THE DOGE/USDT SYMBOL HAS BEEN IDENTIFIED AS HAVING HIGH SETUP QUALITY WITH CLEAR PATTERNS, STRONG SIGNALS INDICATING AN UPTREND ('MOM' +0.0%, 'VOL' 1.0X), AND IS WITHIN THE HEALTH-ADJUSTED RISK BUDGET OF THIS ACCOUNT (2% PER TRADE). WITH A TAKE PROFIT SET AT 60% ABOVE ENTRY PRICE TO CAPITALIZE ON POTENTIAL GAINS WHILE LIMITING DOWNSIDE RISKS, COMBINED WITH A STOP LOSS AT 40% BELOW ENTRY PRICE FOR PROTECTION AGAINST SUDDEN MARKET REVERSALS. THE HISTORICAL CONTEXT SHOWS CONSISTENT PRICING AND SIGNALS THAT SUPPORT THE DECISION OF ENTERING LONG POSITIONS IN DOGE/USDT WITHIN THESE PARAMETERS.", 'ACTION': 'ENTER_NOW'}
2025-07-18 06:30:15,304 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:30:15,304 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-18 06:30:15,304 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:30:15,305 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 9.33s - 3 prompts executed concurrently
2025-07-18 06:30:15,306 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (54.2%) - Decision based on weighted votes: LONG (54.2%)
2025-07-18 06:30:15,306 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (54.2%) - Decision based on weighted votes: LONG (54.2%)
2025-07-18 06:30:15,306 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.2, WAIT: 2.7
2025-07-18 06:30:19,288 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $16.40, Required Margin: $0.82, Account Balance: $29.64
2025-07-18 06:30:19,291 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:30:19,297 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:30:22,604 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:30:36,383 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:30:36,384 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:30:36,385 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:30:36,385 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:30:36,385 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-18 06:30:36,385 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:30:36,385 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:30:36,386 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:30:36,386 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.239309/$0.239310
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.238104 (Distance: 0.50%)
Resistance: $0.240496
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:30:36,387 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:30:40,247 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 155 chars
2025-07-18 06:30:40,247 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM"
}
```...
2025-07-18 06:30:40,247 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 88, Total: 684
2025-07-18 06:30:40,247 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:30:40,248 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:30:40,248 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:30:40,248 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:30:41,720 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 196 chars
2025-07-18 06:30:41,720 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Neutral signal with favorable spread and dynamic key levels"
}...
2025-07-18 06:30:41,720 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 840, Completion: 88, Total: 928
2025-07-18 06:30:41,720 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:30:41,721 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:30:41,721 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:30:41,721 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:30:41,722 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.34s - 2 prompts executed concurrently
2025-07-18 06:30:41,722 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (75.7%) - Decision based on weighted votes: LONG (75.7%)
2025-07-18 06:30:41,722 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (75.7%) - Decision based on weighted votes: LONG (75.7%)
2025-07-18 06:30:41,722 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.8, WAIT: 0.6
2025-07-18 06:31:06,092 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:31:06,092 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:31:06,093 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:31:06,093 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:31:06,093 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:31:06,094 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:31:06,094 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:31:06,095 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:31:06,095 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.238792 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:31:06,094 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:31:06,095 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-18 06:31:06,094 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:31:06,095 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.238759/$0.238760
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.237598 (Distance: 0.50%)
Resistance: $0.239986
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:31:06,096 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:31:06,096 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:31:06,098 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:31:06,099 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-18 06:31:06,102 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:31:11,342 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 732 chars
2025-07-18 06:31:11,342 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 10%, STOP_LOSS: -4%, EXPLANATION: The DOGE/USDT pair shows a neutral sector momentum and normal volatility with excellent liquidity. Given the account's healthy status, it is appropriate to take advantage of this opportunity while adhering to risk manage...
2025-07-18 06:31:11,342 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 938, Completion: 182, Total: 1120
2025-07-18 06:31:11,343 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:31:11,343 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'TAKE_PROFIT': 10.0, 'STOP_LOSS': -4.0, 'EXPLANATION': "THE DOGE/USDT PAIR SHOWS A NEUTRAL SECTOR MOMENTUM AND NORMAL VOLATILITY WITH EXCELLENT LIQUIDITY. GIVEN THE ACCOUNT'S HEALTHY STATUS, IT IS APPROPRIATE TO TAKE ADVANTAGE OF THIS OPPORTUNITY WHILE ADHERING TO RISK MANAGEMENT PRINCIPLES SET BY OUR CRITERIA (2% PER TRADE). A CONSERVATIVE ENTRY STRATEGY ALIGNS WELL DUE TO HISTORICAL STABILITY IN PRICE MOVEMENTS OVER THE LAST FIVE PRICES AND A 'WAIT' SIGNAL WHICH SUGGESTS THAT MARKET CONDITIONS ARE FAVORABLE. THE 1:3 RISK/REWARD RATIO IS ACHIEVABLE WITH CURRENT SETUP QUALITY, ENSURING PROFITABILITY WHILE MAINTAINING ACCOUNT HEALTH INTEGRITY BY NOT EXCEEDING RISK THRESHOLDS OR TOTAL EXPOSURE LIMITS.", 'ACTION': 'ENTER_NOW'}
2025-07-18 06:31:11,343 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:31:11,343 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-18 06:31:11,344 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:31:12,652 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 217 chars
2025-07-18 06:31:12,652 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level at $135 with volume spike.",
  "take_profit": 2.5,
  "stop_loss": 1.7,
  "hold_time": "4min",
  "leverage": 30
}
```...
2025-07-18 06:31:12,653 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 96, Total: 270
2025-07-18 06:31:12,653 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:31:12,653 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:31:12,654 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:31:12,654 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:31:14,240 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 229 chars
2025-07-18 06:31:14,240 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Neutral signals but favorable spread and key levels nearing. Enter with moderate confidence."
}...
2025-07-18 06:31:14,241 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 95, Total: 937
2025-07-18 06:31:14,241 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:31:14,242 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:31:14,242 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:31:14,242 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:31:15,379 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 196 chars
2025-07-18 06:31:15,380 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"risk_adjustment":1.0, "hold_time_target":"8", "entry_threshold":70, "exit_threshold":60, "sizing_method":"FIXED_RISK", "reasoning":"Maintain current strategy - performing well", "confidence":95}...
2025-07-18 06:31:15,380 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 72, Total: 691
2025-07-18 06:31:15,380 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning', 'confidence']
2025-07-18 06:31:15,380 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-18 06:31:15,381 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-18 06:31:15,381 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-18 06:31:15,381 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-18 06:31:15,382 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 9.29s - 4 prompts executed concurrently
2025-07-18 06:31:15,383 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (53.3%) - Decision based on weighted votes: LONG (53.3%)
2025-07-18 06:31:15,383 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (53.3%) - Decision based on weighted votes: LONG (53.3%)
2025-07-18 06:31:15,384 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.4, WAIT: 2.9
2025-07-18 06:31:19,729 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $15.66, Required Margin: $0.78, Account Balance: $28.25
2025-07-18 06:31:19,730 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:31:19,731 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:31:22,063 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:31:36,318 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:31:36,320 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:31:36,320 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:31:36,324 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:31:36,325 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-18 06:31:36,325 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:31:36,330 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:31:36,326 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:31:36,331 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.238834/$0.238835
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.237466 (Distance: 0.50%)
Resistance: $0.239852
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:31:36,333 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:31:40,105 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 194 chars
2025-07-18 06:31:40,105 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Momentum neutral but favorable spread and key levels near"
}...
2025-07-18 06:31:40,106 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 88, Total: 930
2025-07-18 06:31:40,106 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:31:40,106 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:31:40,106 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:31:40,107 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:31:41,447 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 155 chars
2025-07-18 06:31:41,447 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM"
}
```...
2025-07-18 06:31:41,448 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 88, Total: 684
2025-07-18 06:31:41,448 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:31:41,449 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:31:41,449 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:31:41,449 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:31:41,455 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.14s - 2 prompts executed concurrently
2025-07-18 06:31:41,461 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (75.7%) - Decision based on weighted votes: LONG (75.7%)
2025-07-18 06:31:41,461 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (75.7%) - Decision based on weighted votes: LONG (75.7%)
2025-07-18 06:31:41,461 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.8, WAIT: 0.6
2025-07-18 06:32:06,033 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:32:06,034 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:32:06,034 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:32:06,034 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:32:06,035 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-18 06:32:06,035 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:32:06,035 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:32:06,035 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:32:06,036 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.238875/$0.238876
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.237826 (Distance: 0.50%)
Resistance: $0.240216
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:32:06,036 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:32:09,868 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 179 chars
2025-07-18 06:32:09,869 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 78,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 0,
  "REASONING": "Price near key levels with favorable spread"
}...
2025-07-18 06:32:09,869 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 83, Total: 925
2025-07-18 06:32:09,869 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:32:09,870 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:32:09,870 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:32:09,871 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:32:11,164 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 209 chars
2025-07-18 06:32:11,164 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level at +15 pips.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:32:11,164 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 93, Total: 267
2025-07-18 06:32:11,164 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:32:11,165 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:32:11,165 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:32:11,165 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:32:11,166 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.13s - 2 prompts executed concurrently
2025-07-18 06:32:11,166 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:32:11,167 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:32:11,167 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 2.0, WAIT: 2.7
2025-07-18 06:32:15,087 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $14.97, Required Margin: $0.75, Account Balance: $27.01
2025-07-18 06:32:15,087 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:32:15,088 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:32:17,399 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:32:36,401 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:32:36,401 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:32:36,403 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:32:36,403 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:32:36,404 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:32:36,404 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:32:36,404 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:32:36,405 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.239200/$0.239201
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.237905 (Distance: 0.50%)
Resistance: $0.240295
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:32:36,405 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:32:36,412 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-18 06:32:36,412 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:32:36,413 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:32:36,413 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.239100 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:32:36,414 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:32:40,227 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 201 chars
2025-07-18 06:32:40,228 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 78,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 0,
  "REASONING": "Price near key levels with favorable spread and neutral momentum."
}...
2025-07-18 06:32:40,228 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 840, Completion: 86, Total: 926
2025-07-18 06:32:40,228 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:32:40,229 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:32:40,229 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:32:40,229 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:32:41,270 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 148 chars
2025-07-18 06:32:41,270 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "regime": "RANGING_TIGHT",
  "confidence": 75,
  "scalpability": "Medium",
  "recommendedTimeframe": "1m",
  "riskLevel": "Medium"
}
```...
2025-07-18 06:32:41,271 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 67, Total: 663
2025-07-18 06:32:41,271 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalpability', 'recommendedTimeframe', 'riskLevel']
2025-07-18 06:32:41,271 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALPABILITY', 'RECOMMENDEDTIMEFRAME', 'RISKLEVEL']
2025-07-18 06:32:41,271 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:32:41,272 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:32:45,263 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1036 chars
2025-07-18 06:32:45,264 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 60%, STOP_LOSS: -40%, EXPLANATION: The current market data shows a neutral sector momentum with normal volatility and average volume profile. Given the account's healthy status, there is an opportunity to enter conservatively into DOGE/USDT due to its me...
2025-07-18 06:32:45,264 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 936, Completion: 258, Total: 1194
2025-07-18 06:32:45,264 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:32:45,264 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'TAKE_PROFIT': 60.0, 'STOP_LOSS': -40.0, 'EXPLANATION': "THE CURRENT MARKET DATA SHOWS A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE. GIVEN THE ACCOUNT'S HEALTHY STATUS, THERE IS AN OPPORTUNITY TO ENTER CONSERVATIVELY INTO DOGE/USDT DUE TO ITS MEDIUM SETUP QUALITY WHICH ALIGNS WELL WITHIN OUR RISK-ADJUSTED PARAMETERS FOR THIS PARTICULAR TRADING STRATEGY. THE HISTORICAL CONTEXT OF LAST 5 PRICES BEING CONSISTENT WITH CURRENT PRICE AND SIGNALS INDICATING A 'WAIT' APPROACH SUGGESTS THAT THE MARKET MAY BE ENTERING AN UPTREND, JUSTIFYING A LONG POSITION ON DOGE/USDT WHILE MAINTAINING STRICT ADHERENCE TO ACCOUNT PRESERVATION CRITERIA BY SETTING TAKE-PROFIT AT 60% ABOVE ENTRY (TO ENSURE PROFITABILITY) AND STOP LOSS AT -40% BELOW ENTRY PRICE. THIS STRATEGY ALLOWS FOR CAPITALIZING ON POTENTIAL GAINS WITHOUT EXPOSING THE HEALTHY BALANCE OF $1000 EXCESSIVELY, ADHERING TO A MAXIMUM RISK PER TRADE SET BY ACCOUNT CRITERIA WHICH IS 2% OR $20 IN THIS CASE (BASED ON MAX POSITION SIZE).", 'ACTION': 'ENTER_NOW'}
2025-07-18 06:32:45,265 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:32:45,265 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-18 06:32:45,265 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:32:45,266 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 8.87s - 3 prompts executed concurrently
2025-07-18 06:32:45,267 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (86.3%) - Decision based on weighted votes: LONG (86.3%)
2025-07-18 06:32:45,267 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (86.3%) - Decision based on weighted votes: LONG (86.3%)
2025-07-18 06:32:45,267 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.6, WAIT: 0.6
2025-07-18 06:33:06,712 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:33:06,713 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:33:06,714 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:33:06,714 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:33:06,714 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:33:06,714 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:33:06,716 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:33:06,715 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:33:06,716 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:33:06,716 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:33:06,718 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.239703/$0.239704
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.238491 (Distance: 0.50%)
Resistance: $0.240887
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:33:06,716 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-18 06:33:06,717 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:33:06,716 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:33:06,718 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:33:06,720 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:33:06,720 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-18 06:33:06,720 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:33:10,300 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 243 chars
2025-07-18 06:33:10,301 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with volume spike indicating strong buying interest.",
  "take_profit": 2.5,
  "stop_loss": 1.2,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:33:10,301 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 96, Total: 270
2025-07-18 06:33:10,301 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:33:10,301 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:33:10,301 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:33:10,302 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:33:11,801 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 229 chars
2025-07-18 06:33:11,801 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "risk_adjustment": 1.0,
  "hold_time_target": 8,
  "entry_threshold": 70,
  "exit_threshold": 60,
  "sizing_method": "FIXED_RISK",
  "reasoning": "Maintain current strategy - performing well",
  "confidence": 75
}
```...
2025-07-18 06:33:11,801 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 100, Total: 719
2025-07-18 06:33:11,802 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning', 'confidence']
2025-07-18 06:33:11,802 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-18 06:33:11,802 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-18 06:33:11,802 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-18 06:33:11,802 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-18 06:33:12,929 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 155 chars
2025-07-18 06:33:12,930 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "regime": "RANGING_TIGHT",
  "confidence": 75,
  "scalp_suitability": "MEDIUM",
  "recommended_timeframe": "1m",
  "risk_level": "MEDIUM"
}
```...
2025-07-18 06:33:12,930 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 73, Total: 669
2025-07-18 06:33:12,930 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalp_suitability', 'recommended_timeframe', 'risk_level']
2025-07-18 06:33:12,931 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:33:12,931 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:33:12,931 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:33:14,377 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 204 chars
2025-07-18 06:33:14,377 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Price near support level with favorable spread and neutral momentum"
}...
2025-07-18 06:33:14,377 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 87, Total: 929
2025-07-18 06:33:14,377 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:33:14,378 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:33:14,378 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:33:14,378 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:33:14,379 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 7.67s - 4 prompts executed concurrently
2025-07-18 06:33:14,379 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:33:14,380 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:33:14,380 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.8, WAIT: 3.5
2025-07-18 06:33:18,389 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $14.58, Required Margin: $0.73, Account Balance: $26.30
2025-07-18 06:33:18,390 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:33:18,391 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:33:20,734 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:33:36,190 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:33:36,191 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:33:36,191 - core.llm_orchestrator - INFO - 🚀 Submitted 1 prompts for parallel execution
2025-07-18 06:33:36,191 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:33:36,191 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.239776/$0.239777
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.238563 (Distance: 0.50%)
Resistance: $0.240961
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:33:36,192 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:33:39,998 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 202 chars
2025-07-18 06:33:39,998 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 80,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Price near key levels with favorable spread and neutral momentum."
}...
2025-07-18 06:33:39,999 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 87, Total: 929
2025-07-18 06:33:39,999 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:33:39,999 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:33:39,999 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:33:40,000 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:33:40,000 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 3.81s - 1 prompts executed concurrently
2025-07-18 06:33:40,001 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (100.0%) - Decision based on weighted votes: LONG (100.0%)
2025-07-18 06:33:40,001 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (100.0%) - Decision based on weighted votes: LONG (100.0%)
2025-07-18 06:33:40,001 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 2.0
2025-07-18 06:34:06,163 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:34:06,163 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:34:06,164 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:34:06,164 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:34:06,164 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:34:06,164 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:34:06,165 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:34:06,165 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:34:06,165 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:34:06,165 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-18 06:34:06,165 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:34:06,166 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:34:06,165 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:34:06,168 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.239735/$0.239736
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.238488 (Distance: 0.50%)
Resistance: $0.240884
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:34:06,168 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:34:06,169 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:34:06,169 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.239686 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:34:06,170 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:34:09,630 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 138 chars
2025-07-18 06:34:09,631 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "regime": "RANGING_TIGHT",
  "confidence": 75,
  "scalpability": "Medium",
  "recommended_timeframe": "1m",
  "risk_level": "Medium"
}...
2025-07-18 06:34:09,631 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 64, Total: 660
2025-07-18 06:34:09,631 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalpability', 'recommended_timeframe', 'risk_level']
2025-07-18 06:34:09,631 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALPABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:34:09,631 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:34:09,632 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:34:11,163 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 208 chars
2025-07-18 06:34:11,163 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Neutral signal with favorable spread and no pending volume confirmation"
}...
2025-07-18 06:34:11,163 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 90, Total: 932
2025-07-18 06:34:11,164 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:34:11,164 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:34:11,164 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:34:11,164 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:34:13,888 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 641 chars
2025-07-18 06:34:13,888 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 75%, TAKE_PROFIT: 10%, STOP_LOSS: -5%, EXPLANATION: Given the account's healthy status and normal trading parameters along with a high setup quality for DOGE/USDT showing clear patterns, it is recommended to take a conservative long position. The historical context shows ...
2025-07-18 06:34:13,888 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 938, Completion: 164, Total: 1102
2025-07-18 06:34:13,889 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:34:13,889 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 75.0, 'TAKE_PROFIT': 10.0, 'STOP_LOSS': -5.0, 'EXPLANATION': "GIVEN THE ACCOUNT'S HEALTHY STATUS AND NORMAL TRADING PARAMETERS ALONG WITH A HIGH SETUP QUALITY FOR DOGE/USDT SHOWING CLEAR PATTERNS, IT IS RECOMMENDED TO TAKE A CONSERVATIVE LONG POSITION. THE HISTORICAL CONTEXT SHOWS STABILITY IN PRICE WHICH SUPPORTS THIS DECISION. A 10% TAKE PROFIT ALIGNS WELL WITHIN OUR RISK BUDGET OF 2%, WHILE THE STOP LOSS AT -5% ENSURES WE LIMIT POTENTIAL DOWNSIDE RISKS WITHOUT OVERLY CAPPING UPSIDE GAINS, MAINTAINING A BALANCED APPROACH TO CAPITALIZE ON MOMENTUM WITH CONTROLLED EXPOSURE IN LINE WITH ACCOUNT HEALTH CONSIDERATIONS.", 'ACTION': 'ENTER_NOW'}
2025-07-18 06:34:13,890 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:34:13,891 - core.llm_response_parsers - INFO - Opportunity scanner parsed: MOMENTUM (MOMENTUM)
2025-07-18 06:34:13,892 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:34:15,240 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 243 chars
2025-07-18 06:34:15,241 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with volume spike indicating strong buying interest.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:34:15,241 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 96, Total: 270
2025-07-18 06:34:15,241 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:34:15,242 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:34:15,242 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:34:15,242 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:34:15,243 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 9.08s - 4 prompts executed concurrently
2025-07-18 06:34:15,244 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:34:15,244 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:34:15,244 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.2, WAIT: 3.3
2025-07-18 06:34:19,527 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $13.91, Required Margin: $0.70, Account Balance: $25.08
2025-07-18 06:34:19,528 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:34:19,528 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:34:36,173 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:34:36,174 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:34:36,174 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:34:36,175 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:34:36,175 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-18 06:34:36,175 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:34:36,175 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:34:36,176 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.239592/$0.239593
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.238576 (Distance: 0.50%)
Resistance: $0.240974
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:34:36,176 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:34:36,176 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:34:40,008 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 204 chars
2025-07-18 06:34:40,008 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 68,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 0,
  "REASONING": "Price near support level with favorable spread and neutral momentum."
}...
2025-07-18 06:34:40,008 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 86, Total: 928
2025-07-18 06:34:40,009 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:34:40,009 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:34:40,009 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:34:40,009 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:34:41,357 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 155 chars
2025-07-18 06:34:41,357 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM"
}
```...
2025-07-18 06:34:41,358 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 88, Total: 684
2025-07-18 06:34:41,358 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:34:41,358 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:34:41,358 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:34:41,358 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:34:41,359 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.19s - 2 prompts executed concurrently
2025-07-18 06:34:41,360 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (75.1%) - Decision based on weighted votes: LONG (75.1%)
2025-07-18 06:34:41,360 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (75.1%) - Decision based on weighted votes: LONG (75.1%)
2025-07-18 06:34:41,360 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.7, WAIT: 0.6
2025-07-18 06:35:05,852 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:35:05,853 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:35:05,853 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:35:05,853 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:35:05,853 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-18 06:35:05,853 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:35:05,854 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:35:05,854 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:35:05,854 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.239854/$0.239855
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.238394 (Distance: 0.50%)
Resistance: $0.240790
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:35:05,855 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:35:09,628 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 204 chars
2025-07-18 06:35:09,628 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Price near key levels with favorable spread and momentum neutrality"
}...
2025-07-18 06:35:09,629 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 88, Total: 930
2025-07-18 06:35:09,629 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:35:09,629 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:35:09,629 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:35:09,630 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:35:10,904 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 217 chars
2025-07-18 06:35:10,904 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level at $152 with volume spike.",
  "take_profit": 3.0,
  "stop_loss": 1.5,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:35:10,905 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 96, Total: 270
2025-07-18 06:35:10,905 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:35:10,905 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:35:10,905 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:35:10,906 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:35:10,907 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.05s - 2 prompts executed concurrently
2025-07-18 06:35:10,908 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:35:10,908 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:35:10,908 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.8, WAIT: 2.7
2025-07-18 06:35:14,792 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $13.91, Required Margin: $0.70, Account Balance: $25.10
2025-07-18 06:35:14,793 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:35:14,795 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:35:36,352 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:35:36,353 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:35:36,353 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:35:36,353 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:35:36,353 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:35:36,353 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:35:36,354 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:35:36,354 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-18 06:35:36,354 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:35:36,353 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:35:36,354 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:35:36,354 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.240200/$0.240201
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.238795 (Distance: 0.50%)
Resistance: $0.241195
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:35:36,355 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:35:36,355 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.239995 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:35:36,355 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:35:36,356 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-18 06:35:36,356 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:35:36,357 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:35:41,081 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 427 chars
2025-07-18 06:35:41,082 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
    "risk_adjustment": {"min": 0.5, "max": 2.0},
    "hold_time_target": {"minutes": [8, 30]},
    "entry_threshold": {"confidence": 70},
    "exit_threshold": {"confidence": 60},
    "sizing_method": "FIXED_RISK",
    "reasoning": "Maintain current strategy - performing well with a moderate risk ...
2025-07-18 06:35:41,082 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 156, Total: 775
2025-07-18 06:35:41,082 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning', 'confidence']
2025-07-18 06:35:41,083 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-18 06:35:41,083 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-18 06:35:41,083 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-18 06:35:41,083 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-18 06:35:42,504 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 211 chars
2025-07-18 06:35:42,505 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"REGIME": "RANGING_TIGHT", "CONFIDENCE": 75, "SCALP_SUITABILITY": "MEDIUM", "RECOMMENDED_TIMEFRAME": "1m", "RISK_LEVEL": "MEDIUM", "REASONING": "Dynamic regime detection based on volatility and trend analysis"}...
2025-07-18 06:35:42,505 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 90, Total: 686
2025-07-18 06:35:42,506 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-18 06:35:42,506 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-18 06:35:42,507 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:35:42,508 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:35:46,241 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 959 chars
2025-07-18 06:35:46,241 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 85%, TAKE_PROFIT: 10%, STOP_LOSS: -7%, EXPLANATION: Given the account's healthy status and normal trading parameters with a conservative risk profile, there is an opportunity to enter long positions on DOGE/USDT. The historical context shows stability in price (last five ...
2025-07-18 06:35:46,241 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 938, Completion: 235, Total: 1173
2025-07-18 06:35:46,242 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:35:46,242 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 10.0, 'STOP_LOSS': -7.0, 'EXPLANATION': "GIVEN THE ACCOUNT'S HEALTHY STATUS AND NORMAL TRADING PARAMETERS WITH A CONSERVATIVE RISK PROFILE, THERE IS AN OPPORTUNITY TO ENTER LONG POSITIONS ON DOGE/USDT. THE HISTORICAL CONTEXT SHOWS STABILITY IN PRICE (LAST FIVE PRICES ARE CONSISTENT), WHICH ALIGNS WELL WITH OUR PREFERENCE FOR LOW VOLATILITY ENVIRONMENTS AS PER MARKET OVERVIEW DATA. A TAKE PROFIT OF 10% AND STOP LOSS AT -7% PROVIDE A BALANCED RISK-REWARD RATIO, ADHERING TO THE OPPORTUNITY CRITERIA THAT DEMAND HIGH SETUP QUALITY INDICATORS SUCH AS STRONG SIGNALS ('WAIT') WHICH SUGGEST AN UPCOMING POSITIVE PRICE MOVEMENT WITHIN OUR TRADING TIME HORIZON (5-15 MINUTES). THIS DECISION ALSO RESPECTS ACCOUNT PRESERVATION BY NOT EXCEEDING HEALTH-BASED LIMITS AND MAINTAINING A RISK BUDGET OF 2.0% PER TRADE, ENSURING THE OVERALL EXPOSURE REMAINS UNDER CONTROL AT MAX TOTAL EXPOSURE LIMIT SET FOR THIS ACCOUNT'S CAPACITY.", 'ACTION': 'ENTER_NOW'}
2025-07-18 06:35:46,242 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:35:46,242 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-18 06:35:46,242 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:35:47,794 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 201 chars
2025-07-18 06:35:47,795 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Neutral signal but favorable spread and key levels support entry"
}...
2025-07-18 06:35:47,795 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 89, Total: 931
2025-07-18 06:35:47,795 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:35:47,795 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:35:47,796 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:35:47,796 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:35:47,797 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 11.44s - 4 prompts executed concurrently
2025-07-18 06:35:47,797 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (83.4%) - Decision based on weighted votes: LONG (83.4%)
2025-07-18 06:35:47,797 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (83.4%) - Decision based on weighted votes: LONG (83.4%)
2025-07-18 06:35:47,797 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.5, WAIT: 0.7
2025-07-18 06:36:06,202 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:36:06,203 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:36:06,204 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:36:06,204 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:36:06,204 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-18 06:36:06,204 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:36:06,204 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:36:06,205 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:36:06,205 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.240365/$0.240366
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.239202 (Distance: 0.50%)
Resistance: $0.241606
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:36:06,205 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:36:10,051 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 204 chars
2025-07-18 06:36:10,051 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Neutral signals with favorable spread and potential for quick entry"
}...
2025-07-18 06:36:10,051 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 89, Total: 931
2025-07-18 06:36:10,052 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:36:10,052 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:36:10,052 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:36:10,053 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:36:11,285 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 209 chars
2025-07-18 06:36:11,285 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with volume spike.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:36:11,285 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 91, Total: 265
2025-07-18 06:36:11,286 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:36:11,286 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:36:11,286 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:36:11,286 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:36:11,288 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.09s - 2 prompts executed concurrently
2025-07-18 06:36:11,288 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:36:11,288 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:36:11,288 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.8, WAIT: 2.7
2025-07-18 06:36:15,284 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $24.70, Required Margin: $1.23, Account Balance: $44.55
2025-07-18 06:36:15,285 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:36:15,285 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 103.******** DOGE → 1.******** contracts
2025-07-18 06:36:17,643 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:37:21,183 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:37:21,184 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:37:21,184 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:37:21,185 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:37:21,185 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:37:21,185 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:37:21,185 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:37:21,185 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:37:21,186 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:37:21,186 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:37:21,187 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:37:21,187 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:37:21,186 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-18 06:37:21,187 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:37:21,189 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.240726 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:37:21,188 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.240726/$0.240727
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.239522 (Distance: 0.50%)
Resistance: $0.241930
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:37:21,189 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:37:21,189 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:37:24,607 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 148 chars
2025-07-18 06:37:24,607 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
    "regime": "RANGING_TIGHT",
    "confidence": 85,
    "scalpability": "MEDIUM",
    "recommended_timeframe": "1m",
    "risk_level": "Medium"
}...
2025-07-18 06:37:24,607 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 65, Total: 661
2025-07-18 06:37:24,608 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalpability', 'recommended_timeframe', 'risk_level']
2025-07-18 06:37:24,608 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALPABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:37:24,608 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:37:24,609 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:37:28,752 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1052 chars
2025-07-18 06:37:28,753 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 85%, TAKE_PROFIT: 1.3%, STOP_LOSS: -2%, EXPLANATION: The current market data indicates a neutral sector momentum with normal volatility and average volume profile which aligns well with the conservative trading strategy of this account, especially considering its healthy ...
2025-07-18 06:37:28,753 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 938, Completion: 252, Total: 1190
2025-07-18 06:37:28,753 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:37:28,753 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 1.3, 'STOP_LOSS': -2.0, 'EXPLANATION': "THE CURRENT MARKET DATA INDICATES A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE WHICH ALIGNS WELL WITH THE CONSERVATIVE TRADING STRATEGY OF THIS ACCOUNT, ESPECIALLY CONSIDERING ITS HEALTHY STATUS. DOGE/USDT SHOWS NO SIGNIFICANT CHANGE IN MOM (MOVING AVERAGE), MAINTAINING STABILITY WHILE OFFERING LIQUIDITY WITHIN ACCEPTABLE SPREAD LIMITS FOR ENTRY AT $0.240726 USDT. THE SETUP QUALITY IS HIGH WITH A CLEAR PATTERN AND STRONG SIGNALS THAT HAVE NOT CHANGED, SUGGESTING AN OPPORTUNITY TO ENTER CONSERVATIVELY WITHOUT IMMEDIATE RISK OF LOSS DUE TO THE ACCOUNT'S HEALTHY BALANCE AND POSITION LIMIT CRITERIA BEING MET. A TAKE-PROFIT LEVEL HAS BEEN SET AT 130% OF ENTRY PRICE (I.E., $0.297845 USDT) REFLECTING A SLIGHTLY HIGHER REWARD EXPECTATION GIVEN THE CONSERVATIVE RISK APPETITE, WHILE ENSURING THAT STOP LOSS IS WELL BELOW BREAK EVEN TO PROTECT AGAINST SUDDEN MARKET MOVEMENTS WITHIN OUR PREFERRED VOLATILITY RANGE AND ACCOUNT IMPACT LIMITS.", 'ACTION': 'ENTER_NOW'}
2025-07-18 06:37:28,754 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:37:28,754 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-18 06:37:28,754 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:37:30,239 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 260 chars
2025-07-18 06:37:30,239 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with high volume and positive candlestick pattern (bullish engulfing)",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:37:30,239 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 104, Total: 278
2025-07-18 06:37:30,239 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:37:30,240 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:37:30,240 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:37:30,240 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:37:31,824 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 206 chars
2025-07-18 06:37:31,825 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 80,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Neutral signals but favorable spread and recent price near key levels"
}...
2025-07-18 06:37:31,825 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 90, Total: 932
2025-07-18 06:37:31,825 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:37:31,825 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:37:31,826 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:37:31,826 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:37:31,828 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 10.64s - 4 prompts executed concurrently
2025-07-18 06:37:31,828 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (52.6%) - Decision based on weighted votes: LONG (52.6%)
2025-07-18 06:37:31,828 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (52.6%) - Decision based on weighted votes: LONG (52.6%)
2025-07-18 06:37:31,829 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.7, WAIT: 3.3
2025-07-18 06:37:36,827 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $24.70, Required Margin: $1.23, Account Balance: $44.55
2025-07-18 06:37:36,827 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:37:36,828 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 103.******** DOGE → 1.******** contracts
2025-07-18 06:37:39,147 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:38:20,763 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:38:20,764 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:38:20,764 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:38:20,765 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:38:20,765 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:38:20,765 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:38:20,765 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:38:20,765 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:38:20,766 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.240586/$0.240587
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.239447 (Distance: 0.50%)
Resistance: $0.241853
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:38:20,766 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:38:20,766 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-18 06:38:20,766 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:38:20,765 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:38:20,766 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:38:20,767 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:38:20,767 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:38:20,769 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-18 06:38:20,769 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:38:24,833 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 237 chars
2025-07-18 06:38:24,833 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Dynamic regime detection based on volatility and trend analysis"
}
```...
2025-07-18 06:38:24,834 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 110, Total: 706
2025-07-18 06:38:24,834 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-18 06:38:24,834 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-18 06:38:24,834 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:38:24,835 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:38:26,765 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 336 chars
2025-07-18 06:38:26,766 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "risk_adjustment": [0.5, 2.0],
  "hold_time_target": 8,
  "entry_threshold": 70,
  "exit_threshold": 60,
  "sizing_method": "FIXED_RISK",
  "reasoning": "Maintain current strategy - performing well with a slight risk reduction to account for recent poor performance and downward trend in ...
2025-07-18 06:38:26,766 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 124, Total: 743
2025-07-18 06:38:26,767 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning', 'confidence']
2025-07-18 06:38:26,767 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-18 06:38:26,767 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-18 06:38:26,768 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-18 06:38:26,769 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-18 06:38:28,303 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 212 chars
2025-07-18 06:38:28,303 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Current price near support level with favorable spread and neutral signals."
}...
2025-07-18 06:38:28,304 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 841, Completion: 88, Total: 929
2025-07-18 06:38:28,304 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:38:28,304 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:38:28,304 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:38:28,305 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:38:29,632 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 243 chars
2025-07-18 06:38:29,632 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with volume spike indicating strong buying interest.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:38:29,632 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 96, Total: 270
2025-07-18 06:38:29,633 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:38:29,633 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:38:29,633 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:38:29,633 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:38:29,635 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 8.87s - 4 prompts executed concurrently
2025-07-18 06:38:29,636 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:38:29,636 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:38:29,637 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.8, WAIT: 3.5
2025-07-18 06:38:33,981 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $24.51, Required Margin: $1.23, Account Balance: $44.21
2025-07-18 06:38:33,981 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:38:33,982 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 102.******** DOGE → 1.******** contracts
2025-07-18 06:38:36,305 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:39:20,721 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:39:20,722 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:39:20,722 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:39:20,722 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:39:20,723 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:39:20,723 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:39:20,723 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:39:20,723 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:39:20,723 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-18 06:39:20,723 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:39:20,723 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:39:20,724 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:39:20,724 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.240971/$0.240972
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.239626 (Distance: 0.50%)
Resistance: $0.242034
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:39:20,725 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:39:20,725 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:39:20,725 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:39:20,725 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.240830 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:39:20,727 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:39:24,411 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 221 chars
2025-07-18 06:39:24,414 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with strong volume indicators.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:39:24,414 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 92, Total: 266
2025-07-18 06:39:24,415 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:39:24,415 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:39:24,415 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:39:24,416 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:39:28,027 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 904 chars
2025-07-18 06:39:28,028 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 75%, TAKE_PROFIT: 10%, STOP_LOSS: -5%, EXPLANATION: The DOGE/USDT pair shows a slight positive momentum with no significant volatility and the account health allows for moderate risk. A conservative entry is recommended, considering historical data showing stability in pr...
2025-07-18 06:39:28,028 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 937, Completion: 211, Total: 1148
2025-07-18 06:39:28,028 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:39:28,029 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 75.0, 'TAKE_PROFIT': 10.0, 'STOP_LOSS': -5.0, 'EXPLANATION': "THE DOGE/USDT PAIR SHOWS A SLIGHT POSITIVE MOMENTUM WITH NO SIGNIFICANT VOLATILITY AND THE ACCOUNT HEALTH ALLOWS FOR MODERATE RISK. A CONSERVATIVE ENTRY IS RECOMMENDED, CONSIDERING HISTORICAL DATA SHOWING STABILITY IN PRICE MOVEMENT OVER RECENT PERIODS WHICH ALIGNS WELL WITH OUR OPPORTUNITY CRITERIA FOCUSING ON STRONG ALIGNMENT OF MOMENTUM WHILE ENSURING LIQUIDITY AND ACCEPTABLE SPREAD ARE MAINTAINED TO LIMIT SLIPPAGE RISKS WITHIN A 1-3% VOLATILITY RANGE AS PREFERRED BY THE ACCOUNT'S RISK PROFILE. THE TAKE PROFIT IS SET AT 10% ABOVE ENTRY PRICE, WHICH ALIGNS WITH OUR HIGH SETUP QUALITY REQUIREMENT FOR AN ATTRACTIVE REWARD WHILE MAINTAINING SAFETY IN CASE OF SUDDEN MARKET REVERSALS; STOP LOSS IS PLACED SLIGHTLY BELOW TO PROTECT AGAINST DOWNSIDE RISKS WITHOUT BEING TOO AGGRESSIVE GIVEN THE ACCOUNT'S HEALTHY STATUS.", 'ACTION': 'ENTER_NOW'}
2025-07-18 06:39:28,029 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:39:28,030 - core.llm_response_parsers - INFO - Opportunity scanner parsed: MOMENTUM (MOMENTUM)
2025-07-18 06:39:28,030 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:39:29,851 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 237 chars
2025-07-18 06:39:29,852 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Dynamic regime detection based on volatility and trend analysis"
}
```...
2025-07-18 06:39:29,852 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 110, Total: 706
2025-07-18 06:39:29,852 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-18 06:39:29,853 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-18 06:39:29,853 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:39:29,854 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:39:31,467 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 205 chars
2025-07-18 06:39:31,467 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Price near support level with favorable spread and risk/reward ratio"
}...
2025-07-18 06:39:31,468 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 841, Completion: 90, Total: 931
2025-07-18 06:39:31,471 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:39:31,471 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:39:31,471 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:39:31,472 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:39:31,473 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 10.75s - 4 prompts executed concurrently
2025-07-18 06:39:31,475 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:39:31,475 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:39:31,476 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.2, WAIT: 3.3
2025-07-18 06:39:35,898 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $23.36, Required Margin: $1.17, Account Balance: $42.13
2025-07-18 06:39:35,899 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:39:35,899 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:39:38,242 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:40:20,635 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:40:20,636 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:40:20,636 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:40:20,637 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:40:20,637 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:40:20,637 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:40:20,637 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-18 06:40:20,637 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:40:20,637 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:40:20,638 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:40:20,638 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:40:20,638 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.241190/$0.241191
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.239917 (Distance: 0.50%)
Resistance: $0.242329
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:40:20,639 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:40:20,639 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:40:24,431 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 226 chars
2025-07-18 06:40:24,431 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Bullish candlestick pattern with a gap up opening the next session.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:40:24,431 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 97, Total: 271
2025-07-18 06:40:24,431 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:40:24,432 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:40:24,432 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:40:24,432 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:40:26,187 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 237 chars
2025-07-18 06:40:26,187 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Dynamic regime detection based on volatility and trend analysis"
}
```...
2025-07-18 06:40:26,187 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 110, Total: 706
2025-07-18 06:40:26,188 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-18 06:40:26,188 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-18 06:40:26,188 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:40:26,188 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:40:27,789 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 186 chars
2025-07-18 06:40:27,789 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Neutral signal with favorable spread and low risk"
}...
2025-07-18 06:40:27,789 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 87, Total: 929
2025-07-18 06:40:27,790 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:40:27,790 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:40:27,790 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:40:27,791 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:40:27,792 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 7.16s - 3 prompts executed concurrently
2025-07-18 06:40:27,792 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:40:27,792 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:40:27,793 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.8, WAIT: 3.3
2025-07-18 06:40:32,160 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $22.68, Required Margin: $1.13, Account Balance: $40.91
2025-07-18 06:40:32,161 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:40:32,162 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:40:34,542 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:41:20,610 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:41:20,611 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:41:20,611 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:41:20,612 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:41:20,612 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:41:20,612 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:41:20,612 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:41:20,613 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:41:20,613 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:41:20,613 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:41:20,613 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:41:20,613 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:41:20,613 - core.llm_orchestrator - INFO - 🚀 Submitted 5 prompts for parallel execution
2025-07-18 06:41:20,614 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:41:20,614 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.241062/$0.241063
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.239918 (Distance: 0.50%)
Resistance: $0.242330
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:41:20,614 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:41:20,614 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:41:20,616 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.241124 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:41:20,616 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:41:20,616 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-18 06:41:20,617 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:41:20,618 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:41:24,137 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 143 chars
2025-07-18 06:41:24,137 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "regime": "RANGING_TIGHT",
  "confidence": 75,
  "scalp_suitability": "Medium",
  "recommended_timeframe": "1m",
  "risk_level": "Medium"
}...
2025-07-18 06:41:24,137 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 66, Total: 662
2025-07-18 06:41:24,138 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalp_suitability', 'recommended_timeframe', 'risk_level']
2025-07-18 06:41:24,138 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:41:24,138 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:41:24,139 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:41:25,696 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 295 chars
2025-07-18 06:41:25,696 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"risk_adjustment":1.5, "hold_time_target":"6min", "entry_threshold":75, "exit_threshold":65, "sizing_method":"VARIABLE_RISK", "reasoning":"Increase risk to capitalize on current win rate trend and market regime confidence. Adjust hold time for quicker scalping opportunities.", "confidence":80}...
2025-07-18 06:41:25,697 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 95, Total: 714
2025-07-18 06:41:25,697 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning', 'confidence']
2025-07-18 06:41:25,697 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-18 06:41:25,697 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.5x
2025-07-18 06:41:25,698 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.5x, Hold time 8min
2025-07-18 06:41:25,698 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-18 06:41:28,252 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 579 chars
2025-07-18 06:41:28,253 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 85%, TAKE_PROFIT: 70%, STOP_LOSS: 60%, EXPLANATION: Given the account's healthy status and normal trading parameters, along with a high setup quality for DOGE/USDT showing positive momentum without significant volatility or liquidity concerns. The historical context indic...
2025-07-18 06:41:28,253 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 938, Completion: 143, Total: 1081
2025-07-18 06:41:28,253 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:41:28,254 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 70.0, 'STOP_LOSS': 60.0, 'EXPLANATION': "GIVEN THE ACCOUNT'S HEALTHY STATUS AND NORMAL TRADING PARAMETERS, ALONG WITH A HIGH SETUP QUALITY FOR DOGE/USDT SHOWING POSITIVE MOMENTUM WITHOUT SIGNIFICANT VOLATILITY OR LIQUIDITY CONCERNS. THE HISTORICAL CONTEXT INDICATES STABILITY IN PRICE MOVEMENT WHICH ALIGNS WELL WITH OUR CONSERVATIVE APPROACH TO OPPORTUNITIES THAT HAVE STRONG SIGNALS BUT ARE WITHIN THE ACCOUNT'S HEALTH-ADJUSTED RISK BUDGET AND EXPOSURE LIMITS, ENSURING WE MAINTAIN A FOCUS ON PRESERVATION WHILE SEEKING PROFITABLE TRADES.", 'ACTION': 'ENTER_NOW'}
2025-07-18 06:41:28,254 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:41:28,254 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-18 06:41:28,254 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:41:29,646 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 224 chars
2025-07-18 06:41:29,646 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a strong volume spike.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:41:29,647 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 93, Total: 267
2025-07-18 06:41:29,647 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:41:29,647 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:41:29,647 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:41:29,648 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:41:31,225 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 187 chars
2025-07-18 06:41:31,226 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Neutral signals with favorable spread and low risk"
}...
2025-07-18 06:41:31,226 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 87, Total: 929
2025-07-18 06:41:31,226 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:41:31,227 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:41:31,227 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:41:31,227 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:41:31,229 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 10.62s - 5 prompts executed concurrently
2025-07-18 06:41:31,230 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:41:31,230 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:41:31,231 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.5, WAIT: 3.5
2025-07-18 06:41:59,890 - main - INFO - Epinnox v6 starting up...
2025-07-18 06:41:59,915 - core.performance_monitor - INFO - Performance monitoring started
2025-07-18 06:41:59,915 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-18 06:41:59,916 - main - INFO - Performance monitoring initialized
2025-07-18 06:41:59,929 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-18 06:41:59,930 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-18 06:41:59,931 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-18 06:42:05,269 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-18 06:42:10,205 - core.emergency_stop_coordinator - INFO - Emergency Stop Coordinator initialized
2025-07-18 06:42:10,206 - core.emergency_stop_coordinator - INFO - Emergency Stop Coordinator initialized
2025-07-18 06:42:10,206 - core.emergency_stop_coordinator - INFO - [OK] Registered module for emergency stop: main_window
2025-07-18 06:42:11,624 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-18 06:42:12,546 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-18 06:42:13,824 - websocket - INFO - Websocket connected
2025-07-18 06:42:16,163 - trading.position_tracker - INFO - Position Tracker initialized
2025-07-18 06:42:16,569 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-18 06:42:16,569 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-18 06:42:16,570 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-18 06:42:16,570 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-18 06:42:16,576 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-18 06:42:18,626 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-18 06:42:18,626 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-18 06:42:18,626 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-18 06:42:18,629 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-18 06:42:18,630 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-18 06:42:18,630 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-18 06:42:18,630 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-18 06:42:18,630 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-18 06:42:18,637 - core.signal_hierarchy - INFO - Signal Hierarchy initialized
2025-07-18 06:42:18,660 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-18 06:42:18,660 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-18 06:42:18,660 - storage.session_manager - INFO - Session Manager initialized
2025-07-18 06:42:18,665 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250718_064218_7288bfc6
2025-07-18 06:42:18,666 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250718_064218_7288bfc6
2025-07-18 06:42:18,822 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-18 06:42:18,826 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-18 06:42:18,826 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-18 06:42:18,826 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-18 06:42:18,827 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-18 06:42:18,827 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-18 06:42:18,829 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-18 06:42:18,833 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-18 06:42:18,834 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-18 06:42:18,835 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Registered timer 'symbol_scanner_update': interval=30.0s, priority=MEDIUM
2025-07-18 06:42:18,835 - core.symbol_scanner - INFO - [SYMBOL_SCANNER] Registered with timer coordinator (interval: 30.0s)
2025-07-18 06:42:18,836 - core.llm_action_executors - WARNING - ⚠️ Intelligent Limit Order Manager not available
2025-07-18 06:42:18,836 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-18 06:50:11,984 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-18 06:51:00,787 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:51:00,791 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:51:00,791 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:51:00,791 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:51:00,791 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:51:00,791 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:51:00,791 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:51:00,792 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:51:00,792 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:51:00,792 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:51:00,792 - core.llm_orchestrator - INFO - 🚀 Submitted 5 prompts for parallel execution
2025-07-18 06:51:00,793 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:51:00,793 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:51:00,793 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:51:00,794 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:51:00,795 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.240521/$0.240522
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.239258 (Distance: 0.50%)
Resistance: $0.241662
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:51:00,795 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:51:00,795 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.240460 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:51:00,795 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-18 06:51:00,795 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:51:00,796 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:51:00,796 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:51:04,437 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 232 chars
2025-07-18 06:51:04,437 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "regime": "RANGING_TIGHT",
  "confidence": 75,
  "scalpability": "MEDIUM",
  "recommended_timeframe": "1m",
  "risk_level": "MEDIUM",
  "reasoning": "Dynamic regime detection based on volatility and trend analysis"
}
```...
2025-07-18 06:51:04,437 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 91, Total: 687
2025-07-18 06:51:04,439 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalpability', 'recommended_timeframe', 'risk_level', 'reasoning']
2025-07-18 06:51:04,439 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALPABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-18 06:51:04,440 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:51:04,440 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:51:05,930 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 201 chars
2025-07-18 06:51:05,930 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Neutral signal but favorable spread and price near support level"
}...
2025-07-18 06:51:05,930 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 841, Completion: 89, Total: 930
2025-07-18 06:51:05,931 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:51:05,931 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:51:05,931 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:51:05,931 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:51:07,381 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 269 chars
2025-07-18 06:51:07,381 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with volume spike and positive candlestick pattern (e.g., hammer or engulfing)",
  "take_profit": 2.5,
  "stop_loss": 1.3,
  "hold_time": "4min",
  "leverage": 30
}
```...
2025-07-18 06:51:07,382 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 109, Total: 283
2025-07-18 06:51:07,382 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:51:07,382 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:51:07,382 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:51:07,383 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:51:11,558 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1189 chars
2025-07-18 06:51:11,558 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "risk_adjustment": 1.0, // Keeping risk at current level due to stable performance despite a downward trend in win rate and normal market conditions.
  "hold_time_target": 8, // Maintaining the average hold time of 8 minutes as it aligns with medium scalp suitability under normal volatility regi...
2025-07-18 06:51:11,558 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 296, Total: 915
2025-07-18 06:51:11,558 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:51:11,561 - core.llm_response_parsers - WARNING - 🔍 No structured data found in: {
  "risk_adjustment": 1.0, 
  "hold_time_target": 8, 
  "entry_threshold": 70, 
  "exit_threshold":...
2025-07-18 06:51:11,561 - core.llm_response_parsers - WARNING - ⚠️ No JSON or structured data found in response
2025-07-18 06:51:11,561 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-18 06:51:11,561 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-18 06:51:15,101 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 896 chars
2025-07-18 06:51:15,101 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 85%, TAKE_PROFIT: 10%, STOP_LOSS: -3%, EXPLANATION: The DOGE/USDT pair shows a neutral sector momentum with normal volatility and average volume profile. Given the account's healthy status, it allows for moderate risk exposure while adhering to preservation criteria of 2%...
2025-07-18 06:51:15,102 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 937, Completion: 226, Total: 1163
2025-07-18 06:51:15,102 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:51:15,102 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 10.0, 'STOP_LOSS': -3.0, 'EXPLANATION': "THE DOGE/USDT PAIR SHOWS A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE. GIVEN THE ACCOUNT'S HEALTHY STATUS, IT ALLOWS FOR MODERATE RISK EXPOSURE WHILE ADHERING TO PRESERVATION CRITERIA OF 2% MAX PER TRADE AND KEEPING TOTAL EXPOSURE UNDER 70%. WITH HIGH SETUP QUALITY INDICATED BY CLEAR PATTERNS OR STRONG SIGNALS (THOUGH CURRENTLY 'WAIT'), A CONSERVATIVE ENTRY IS RECOMMENDED. THE HISTORICAL CONTEXT SHOWS STABILITY IN THE LAST FIVE PRICES, SUGGESTING THAT ENTERING NOW COULD BE OPPORTUNE IF MOMENTUM ALIGNS WITH OUR CRITERIA FOR HEALTH-ADJUSTED OPPORTUNITIES AND RISK/REWARD RATIO OF >3:1 CAN BE ACHIEVED WITHIN THIS VOLATILITY RANGE (CONSERVATIVELY 1-3%). A TAKE PROFIT AT +10% ENSURES A REASONABLE RETURN WHILE THE STOP LOSS SET TO -3% PROTECTS AGAINST SIGNIFICANT ADVERSE MOVEMENTS.", 'ACTION': 'ENTER_NOW'}
2025-07-18 06:51:15,103 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:51:15,103 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-18 06:51:15,103 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:51:15,104 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 14.32s - 5 prompts executed concurrently
2025-07-18 06:51:15,105 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (50.5%) - Decision based on weighted votes: LONG (50.5%)
2025-07-18 06:51:15,105 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (50.5%) - Decision based on weighted votes: LONG (50.5%)
2025-07-18 06:51:15,105 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.5, WAIT: 3.4
2025-07-18 06:51:19,561 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $21.89, Required Margin: $1.09, Account Balance: $39.48
2025-07-18 06:51:19,561 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:51:19,563 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:51:21,962 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:51:30,760 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:51:30,760 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:51:30,761 - core.llm_orchestrator - INFO - 🚀 Submitted 1 prompts for parallel execution
2025-07-18 06:51:30,761 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:51:30,761 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.240377/$0.240378
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.239140 (Distance: 0.50%)
Resistance: $0.241544
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:51:30,761 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:51:34,543 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 205 chars
2025-07-18 06:51:34,543 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Price near support level with favorable spread and neutral momentum."
}...
2025-07-18 06:51:34,543 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 87, Total: 929
2025-07-18 06:51:34,544 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:51:34,544 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:51:34,544 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:51:34,544 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:51:34,545 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 3.79s - 1 prompts executed concurrently
2025-07-18 06:51:34,545 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (100.0%) - Decision based on weighted votes: LONG (100.0%)
2025-07-18 06:51:34,546 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (100.0%) - Decision based on weighted votes: LONG (100.0%)
2025-07-18 06:51:34,546 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.8
2025-07-18 06:52:01,001 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:52:01,002 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:52:01,002 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:52:01,002 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:52:01,003 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:52:01,003 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:52:01,003 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:52:01,004 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:52:01,004 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:52:01,004 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-18 06:52:01,004 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:52:01,004 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:52:01,005 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.240071/$0.240072
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.239003 (Distance: 0.50%)
Resistance: $0.241405
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:52:01,006 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:52:01,006 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:52:01,006 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:52:01,006 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.240204 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:52:01,007 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:52:04,830 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 237 chars
2025-07-18 06:52:04,831 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "regime": "RANGING_TIGHT",
  "confidence": 75,
  "scalp_suitability": "MEDIUM",
  "recommended_timeframe": "1m",
  "risk_level": "MEDIUM",
  "reasoning": "Dynamic regime detection based on volatility and trend analysis"
}
```...
2025-07-18 06:52:04,831 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 93, Total: 689
2025-07-18 06:52:04,832 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalp_suitability', 'recommended_timeframe', 'risk_level', 'reasoning']
2025-07-18 06:52:04,832 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-18 06:52:04,832 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:52:04,832 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:52:06,121 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-18 06:52:06,122 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke with a sudden upward trend of more than 2 pip.",
  "take_profit": 1.5,
  "stop_loss": 0.4,
  "hold_time": "3min",
  "leverage": 30
}
```...
2025-07-18 06:52:06,122 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 97, Total: 271
2025-07-18 06:52:06,122 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:52:06,123 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:52:06,123 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:52:06,123 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:52:10,015 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1078 chars
2025-07-18 06:52:10,016 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 10%, STOP_LOSS: -3%, EXPLANATION: The current market environment is stable with normal volatility and a healthy account status. Given the high setup quality of DOGE/USDT, which shows no significant change in mom (moving average) but has an adequate volum...
2025-07-18 06:52:10,016 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 938, Completion: 252, Total: 1190
2025-07-18 06:52:10,017 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:52:10,018 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'TAKE_PROFIT': 10.0, 'STOP_LOSS': -3.0, 'EXPLANATION': "THE CURRENT MARKET ENVIRONMENT IS STABLE WITH NORMAL VOLATILITY AND A HEALTHY ACCOUNT STATUS. GIVEN THE HIGH SETUP QUALITY OF DOGE/USDT, WHICH SHOWS NO SIGNIFICANT CHANGE IN MOM (MOVING AVERAGE) BUT HAS AN ADEQUATE VOLUME LEVEL FOR LIQUIDITY PURPOSES, IT PRESENTS ITSELF AS A CONSERVATIVE ENTRY OPPORTUNITY SUITABLE WITHIN OUR RISK-ADJUSTED PARAMETERS. THE HISTORICAL CONTEXT INDICATES THAT THERE HAVE BEEN 'WAIT' SIGNALS RECENTLY; HOWEVER, THE LACK OF NEGATIVE MOMENTUM AND STABLE PRICE SUGGESTS CAUTIOUS OPTIMISM TOWARDS THIS CRYPTOCURRENCY PAIRING UNDER CURRENT CONDITIONS. A TAKE PROFIT AT 10% ABOVE THE PURCHASE PRICE IS SET TO ENSURE A REASONABLE RETURN ON INVESTMENT WHILE MAINTAINING ACCOUNT HEALTH BY NOT EXCEEDING OUR RISK BUDGET PER TRADE. THE STOP LOSS AT -3% SERVES AS AN ADDITIONAL SAFEGUARD AGAINST SUDDEN MARKET DOWNTURNS, ALIGNING WITH CONSERVATIVE VOLATILITY PREFERENCES AND ENSURING THAT ANY POTENTIAL LOSSES ARE CONTAINED WITHIN ACCEPTABLE LIMITS FOR A 'HEALTHY' ACCOUNT STATUS.", 'ACTION': 'ENTER_NOW'}
2025-07-18 06:52:10,018 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:52:10,018 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-18 06:52:10,019 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:52:11,574 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 202 chars
2025-07-18 06:52:11,575 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Neutral signal but favorable spread and recent price near support"
}...
2025-07-18 06:52:11,575 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 89, Total: 931
2025-07-18 06:52:11,576 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:52:11,576 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:52:11,576 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:52:11,576 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:52:11,577 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 10.58s - 4 prompts executed concurrently
2025-07-18 06:52:11,578 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (50.7%) - Decision based on weighted votes: LONG (50.7%)
2025-07-18 06:52:11,578 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (50.7%) - Decision based on weighted votes: LONG (50.7%)
2025-07-18 06:52:11,578 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.4, WAIT: 3.3
2025-07-18 06:52:15,658 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $21.13, Required Margin: $1.06, Account Balance: $38.12
2025-07-18 06:52:15,658 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:52:15,659 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:52:18,053 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:52:30,840 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:52:30,841 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:52:30,841 - core.llm_orchestrator - INFO - 🚀 Submitted 1 prompts for parallel execution
2025-07-18 06:52:30,842 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:52:30,842 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.239767/$0.239768
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.238559 (Distance: 0.50%)
Resistance: $0.240957
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:52:30,843 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:52:34,590 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 180 chars
2025-07-18 06:52:34,591 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 60,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Price near key levels with favorable spread"
}...
2025-07-18 06:52:34,591 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 84, Total: 926
2025-07-18 06:52:34,591 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:52:34,591 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:52:34,591 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:52:34,591 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:52:34,592 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 3.75s - 1 prompts executed concurrently
2025-07-18 06:52:34,592 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (100.0%) - Decision based on weighted votes: LONG (100.0%)
2025-07-18 06:52:34,593 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (100.0%) - Decision based on weighted votes: LONG (100.0%)
2025-07-18 06:52:34,593 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.5
2025-07-18 06:53:00,956 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:53:00,957 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:53:00,958 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:53:00,958 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:53:00,958 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:53:00,958 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:53:00,958 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:53:00,959 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:53:00,959 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:53:00,959 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-18 06:53:00,959 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:53:00,959 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:53:00,960 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.239772/$0.239773
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.238599 (Distance: 0.50%)
Resistance: $0.240997
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:53:00,960 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:53:00,960 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:53:00,961 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:53:00,961 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-18 06:53:00,962 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:53:05,000 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 390 chars
2025-07-18 06:53:05,001 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"risk_adjustment":1.5, "hold_time_target":"6min", "entry_threshold":75, "exit_threshold":65, "sizing_method":"VARIABLE_RISK", "reasoning":"Increase risk to capitalize on current win rate trend while reducing hold time for quicker scalping opportunities. Adjust entry and exit thresholds slightly hig...
2025-07-18 06:53:05,001 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 113, Total: 732
2025-07-18 06:53:05,001 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning', 'confidence']
2025-07-18 06:53:05,001 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-18 06:53:05,001 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.5x
2025-07-18 06:53:05,001 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.5x, Hold time 8min
2025-07-18 06:53:05,002 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-18 06:53:07,252 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 447 chars
2025-07-18 06:53:07,252 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"REGIME": "RANGING_TIGHT", "CONFIDENCE": 85, "SCALP_SUITABILITY": "MEDIUM", "RECOMMENDED_TIMEFRAME": "1m", "RISK_LEVEL": "MEDIUM", "REASONING": "The current regime is classified as 'RANGING_TIGHT' due to the neutral trend and medium volatility. The confidence level of 85% reflects a cautious optimi...
2025-07-18 06:53:07,252 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 148, Total: 744
2025-07-18 06:53:07,252 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 184 (char 183)
2025-07-18 06:53:07,253 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:53:08,811 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 198 chars
2025-07-18 06:53:08,811 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 60,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Neutral signals with favorable spread and basic level support"
}...
2025-07-18 06:53:08,811 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 88, Total: 930
2025-07-18 06:53:08,811 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:53:08,811 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:53:08,811 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:53:08,812 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:53:10,062 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 225 chars
2025-07-18 06:53:10,063 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with strong volume increase.",
  "take_profit": 2.5,
  "stop_loss": 1.7,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:53:10,063 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 91, Total: 265
2025-07-18 06:53:10,063 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:53:10,063 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:53:10,064 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:53:10,064 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:53:10,065 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 9.11s - 4 prompts executed concurrently
2025-07-18 06:53:10,066 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:53:10,066 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:53:10,066 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.5, WAIT: 3.3
2025-07-18 06:53:13,982 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $20.43, Required Margin: $1.02, Account Balance: $36.85
2025-07-18 06:53:13,982 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:53:13,983 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:53:16,312 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:53:30,822 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:53:30,823 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:53:30,823 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:53:30,823 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.239955/$0.239956
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.238707 (Distance: 0.50%)
Resistance: $0.241107
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:53:30,823 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:53:30,823 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-18 06:53:30,824 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:53:30,825 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:53:30,825 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.239907 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:53:30,826 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:53:34,716 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 202 chars
2025-07-18 06:53:34,716 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 68,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 0,
  "REASONING": "Price near support level with favorable spread and neutral signals"
}...
2025-07-18 06:53:34,717 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 86, Total: 928
2025-07-18 06:53:34,717 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:53:34,717 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:53:34,717 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:53:34,717 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:53:37,155 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 546 chars
2025-07-18 06:53:37,155 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 85%, TAKE_PROFIT: 1%, STOP_LOSS: -2%, EXPLANATION: The DOGE/USDT pair shows a positive mom with no significant volatility change and maintains an average volume profile. Given the account's healthy status, normal trading parameters are acceptable for entry into this oppor...
2025-07-18 06:53:37,155 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 938, Completion: 141, Total: 1079
2025-07-18 06:53:37,156 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:53:37,156 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 1.0, 'STOP_LOSS': -2.0, 'EXPLANATION': "THE DOGE/USDT PAIR SHOWS A POSITIVE MOM WITH NO SIGNIFICANT VOLATILITY CHANGE AND MAINTAINS AN AVERAGE VOLUME PROFILE. GIVEN THE ACCOUNT'S HEALTHY STATUS, NORMAL TRADING PARAMETERS ARE ACCEPTABLE FOR ENTRY INTO THIS OPPORTUNITY WHICH ALSO ALIGNS WELL WITHIN OUR RISK BUDGET OF 2% PER TRADE WHILE RESPECTING MAXIMUM TOTAL EXPOSURE LIMITS AT ONLY $70%. THE TAKE PROFIT IS SET CONSERVATIVELY TO ENSURE CAPITAL PRESERVATION IN LINE WITH A MODERATE VOLATILITY ENVIRONMENT.", 'ACTION': 'ENTER_NOW'}
2025-07-18 06:53:37,156 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:53:37,156 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-18 06:53:37,156 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:53:37,157 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 6.34s - 2 prompts executed concurrently
2025-07-18 06:53:37,157 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (100.0%) - Decision based on weighted votes: LONG (100.0%)
2025-07-18 06:53:37,158 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (100.0%) - Decision based on weighted votes: LONG (100.0%)
2025-07-18 06:53:37,158 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.4
2025-07-18 06:54:00,894 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:54:00,895 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:54:00,895 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:54:00,895 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:54:00,895 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:54:00,896 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:54:00,896 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:54:00,896 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:54:00,896 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-18 06:54:00,896 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:54:00,897 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:54:00,897 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:54:00,898 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.240058/$0.240059
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.238707 (Distance: 0.50%)
Resistance: $0.241107
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:54:00,898 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:54:04,419 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 150 chars
2025-07-18 06:54:04,420 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "regime": "RANGING_TIGHT",
  "confidence": 75,
  "scalpability": "MEDIUM",
  "recommended_timeframe": "1m",
  "risk_level": "MEDIUM"
}
```...
2025-07-18 06:54:04,420 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 71, Total: 667
2025-07-18 06:54:04,420 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalpability', 'recommended_timeframe', 'risk_level']
2025-07-18 06:54:04,421 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALPABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:54:04,421 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:54:04,421 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:54:05,766 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 245 chars
2025-07-18 06:54:05,766 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 92,
  "entry_reason": "Breakout above key resistance level with high volume and positive candlestick pattern.",
  "take_profit": 1.5,
  "stop_loss": 0.8,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:54:05,767 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 98, Total: 272
2025-07-18 06:54:05,767 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:54:05,767 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:54:05,767 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:54:05,767 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:54:07,333 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 198 chars
2025-07-18 06:54:07,334 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 80,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Neutral signals with favorable spread and key levels nearing."
}...
2025-07-18 06:54:07,334 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 89, Total: 931
2025-07-18 06:54:07,334 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:54:07,334 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:54:07,334 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:54:07,335 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:54:07,335 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 6.44s - 3 prompts executed concurrently
2025-07-18 06:54:07,336 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:54:07,336 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:54:07,337 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 2.0, WAIT: 3.3
2025-07-18 06:54:11,922 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $20.14, Required Margin: $1.01, Account Balance: $35.82
2025-07-18 06:54:11,922 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:54:11,924 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:54:14,275 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:54:30,961 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:54:30,962 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:54:30,963 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:54:30,963 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:54:30,963 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:54:30,963 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-18 06:54:30,963 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:54:30,963 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:54:30,964 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:54:30,964 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.240380/$0.240381
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.239114 (Distance: 0.50%)
Resistance: $0.241518
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:54:30,964 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:54:30,965 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:54:30,965 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.240316 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:54:30,966 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:54:34,381 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 155 chars
2025-07-18 06:54:34,381 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "regime": "RANGING_TIGHT",
  "confidence": 75,
  "scalp_suitability": "MEDIUM",
  "recommended_timeframe": "1m",
  "risk_level": "MEDIUM"
}
```...
2025-07-18 06:54:34,381 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 73, Total: 669
2025-07-18 06:54:34,382 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalp_suitability', 'recommended_timeframe', 'risk_level']
2025-07-18 06:54:34,382 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:54:34,382 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:54:34,382 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:54:37,595 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 775 chars
2025-07-18 06:54:37,595 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 70%, STOP_LOSS: -10%, EXPLANATION: The current market conditions are favorable for a conservative long position on DOGE/USDT. With the account in good health and normal trading parameters, there is confidence to take advantage of this opportunity with an...
2025-07-18 06:54:37,595 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 938, Completion: 193, Total: 1131
2025-07-18 06:54:37,595 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:54:37,596 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'TAKE_PROFIT': 70.0, 'STOP_LOSS': -10.0, 'EXPLANATION': 'THE CURRENT MARKET CONDITIONS ARE FAVORABLE FOR A CONSERVATIVE LONG POSITION ON DOGE/USDT. WITH THE ACCOUNT IN GOOD HEALTH AND NORMAL TRADING PARAMETERS, THERE IS CONFIDENCE TO TAKE ADVANTAGE OF THIS OPPORTUNITY WITH AN EMPHASIS ON PRESERVING CAPITAL WHILE AIMING FOR PROFITABILITY WITHIN ACCEPTABLE RISK LEVELS. A 70% TARGET PRICE ALIGNS WELL WITH OUR MAXIMUM EXPOSURE LIMIT, ENSURING THAT WE DO NOT EXCEED IT EVEN IF THE POSITION MOVES FAVORABLY BY A SIGNIFICANT MARGIN. THE STOP LOSS IS SET AT -10% TO PROTECT AGAINST SUDDEN ADVERSE MARKET MOVEMENTS WITHOUT BEING OVERLY CONSERVATIVE AND MISSING OUT ON POTENTIAL GAINS DUE TO MINOR FLUCTUATIONS WITHIN OUR PREFERRED VOLATILITY RANGE OF 1-3%.', 'ACTION': 'ENTER_NOW'}
2025-07-18 06:54:37,596 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:54:37,596 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-18 06:54:37,596 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:54:39,221 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 205 chars
2025-07-18 06:54:39,222 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Price near support level with favorable spread and risk/reward ratio"
}...
2025-07-18 06:54:39,222 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 90, Total: 932
2025-07-18 06:54:39,222 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:54:39,222 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:54:39,223 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:54:39,223 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:54:39,224 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 8.26s - 3 prompts executed concurrently
2025-07-18 06:54:39,224 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (85.6%) - Decision based on weighted votes: LONG (85.6%)
2025-07-18 06:54:39,224 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (85.6%) - Decision based on weighted votes: LONG (85.6%)
2025-07-18 06:54:39,224 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.4, WAIT: 0.6
2025-07-18 06:55:00,961 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:55:00,962 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:55:00,962 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:55:00,962 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:55:00,963 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:55:00,963 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:55:00,963 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:55:00,963 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:55:00,963 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:55:00,963 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-18 06:55:00,964 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:55:00,964 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:55:00,964 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:55:00,964 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.240373/$0.240374
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.239164 (Distance: 0.50%)
Resistance: $0.241568
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:55:00,965 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:55:00,965 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-18 06:55:00,965 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:55:00,966 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:55:04,879 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 197 chars
2025-07-18 06:55:04,879 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Neutral signal with favorable spread and basic level support"
}...
2025-07-18 06:55:04,880 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 88, Total: 930
2025-07-18 06:55:04,880 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:55:04,880 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:55:04,880 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:55:04,880 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:55:06,075 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 197 chars
2025-07-18 06:55:06,075 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"risk_adjustment":1.0, "hold_time_target":"8", "entry_threshold":70, "exit_threshold":60, "sizing_method":"FIXED_RISK", "reasoning":"Maintain current strategy - performing well", "confidence":75}
...
2025-07-18 06:55:06,075 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 73, Total: 692
2025-07-18 06:55:06,076 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning', 'confidence']
2025-07-18 06:55:06,076 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-18 06:55:06,076 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-18 06:55:06,076 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-18 06:55:06,077 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-18 06:55:07,333 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 219 chars
2025-07-18 06:55:07,333 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with strong volume increase.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:55:07,333 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 91, Total: 265
2025-07-18 06:55:07,333 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:55:07,334 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:55:07,334 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:55:07,334 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:55:09,148 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 237 chars
2025-07-18 06:55:09,149 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Dynamic regime detection based on volatility and trend analysis"
}
```...
2025-07-18 06:55:09,149 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 110, Total: 706
2025-07-18 06:55:09,149 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-18 06:55:09,150 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-18 06:55:09,150 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:55:09,150 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:55:09,151 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 8.19s - 4 prompts executed concurrently
2025-07-18 06:55:09,151 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:55:09,152 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:55:09,152 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.8, WAIT: 3.5
2025-07-18 06:55:13,120 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $19.28, Required Margin: $0.96, Account Balance: $34.77
2025-07-18 06:55:13,121 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:55:13,121 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:55:15,468 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:55:30,944 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:55:30,945 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:55:30,945 - core.llm_orchestrator - INFO - 🚀 Submitted 1 prompts for parallel execution
2025-07-18 06:55:30,945 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:55:30,946 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.240387/$0.240388
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.239194 (Distance: 0.50%)
Resistance: $0.241598
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:55:30,946 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:55:34,931 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 201 chars
2025-07-18 06:55:34,931 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Neutral signals but favorable spread and key levels near support"
}...
2025-07-18 06:55:34,932 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 89, Total: 931
2025-07-18 06:55:34,932 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:55:34,932 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:55:34,932 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:55:34,932 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:55:34,933 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 3.99s - 1 prompts executed concurrently
2025-07-18 06:55:34,933 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (100.0%) - Decision based on weighted votes: LONG (100.0%)
2025-07-18 06:55:34,933 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (100.0%) - Decision based on weighted votes: LONG (100.0%)
2025-07-18 06:55:34,934 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.8
2025-07-18 06:56:01,007 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:56:01,008 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:56:01,008 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:56:01,008 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:56:01,008 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:56:01,008 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:56:01,008 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:56:01,009 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:56:01,009 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-18 06:56:01,009 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:56:01,009 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:56:01,009 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:56:01,010 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.240460/$0.240461
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.239276 (Distance: 0.50%)
Resistance: $0.241680
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:56:01,010 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:56:01,011 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:56:01,011 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:56:01,011 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.240478 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:56:01,012 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:56:04,929 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 200 chars
2025-07-18 06:56:04,929 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 60,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Neutral signals with favorable spread and low risk."
}
```...
2025-07-18 06:56:04,930 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 92, Total: 934
2025-07-18 06:56:04,930 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:56:04,930 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:56:04,931 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:56:04,931 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:56:06,672 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 329 chars
2025-07-18 06:56:06,672 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "regime": "RANGING_TIGHT",
  "confidence": 75,
  "scalp_suitability": "MEDIUM",
  "recommended_timeframe": "1m",
  "risk_level": "MEDIUM",
  "reasoning": "Dynamic regime detection based on volatility and trend analysis indicates a medium suitability for scalping in the current RANGING_TI...
2025-07-18 06:56:06,672 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 112, Total: 708
2025-07-18 06:56:06,673 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalp_suitability', 'recommended_timeframe', 'risk_level', 'reasoning']
2025-07-18 06:56:06,673 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-18 06:56:06,673 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:56:06,674 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:56:09,833 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 755 chars
2025-07-18 06:56:09,834 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 10%, STOP_LOSS: -3%, EXPLANATION: The current market data shows a stable and healthy account status with normal trading parameters. Given the high setup quality of DOGE/USDT, which has shown consistent momentum without any recent changes in mom or volati...
2025-07-18 06:56:09,834 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 938, Completion: 187, Total: 1125
2025-07-18 06:56:09,834 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:56:09,834 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'TAKE_PROFIT': 10.0, 'STOP_LOSS': -3.0, 'EXPLANATION': 'THE CURRENT MARKET DATA SHOWS A STABLE AND HEALTHY ACCOUNT STATUS WITH NORMAL TRADING PARAMETERS. GIVEN THE HIGH SETUP QUALITY OF DOGE/USDT, WHICH HAS SHOWN CONSISTENT MOMENTUM WITHOUT ANY RECENT CHANGES IN MOM OR VOLATILITY (BOTH AT 0%), IT IS RECOMMENDED TO ENTER INTO THIS POSITION CONSERVATIVELY WHILE MAINTAINING STRICT RISK MANAGEMENT PROTOCOLS ALIGNED WITH OUR HEALTH-ADJUSTED CRITERIA. THE TAKE PROFIT AND STOP LOSS ARE SET WITHIN THE MODERATE RANGE OF EXPECTED PRICE FLUCTUATIONS FOR A STABLE CRYPTOCURRENCY LIKE DOGE/USDT, ENSURING THAT WE CAPITALIZE ON POTENTIAL GAINS WITHOUT EXPOSING OURSELVES TO EXCESSIVE RISKS WHICH COULD JEOPARDIZE OUR ACCOUNT HEALTH STATUS.', 'ACTION': 'ENTER_NOW'}
2025-07-18 06:56:09,835 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:56:09,835 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-18 06:56:09,835 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:56:11,347 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 277 chars
2025-07-18 06:56:11,347 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a sudden surge of volume and positive news on the company's earnings report.",
  "take_profit": 2.5,
  "stop_loss": 1.7,
  "hold_time": "30s",
  "leverage": 40
}
```...
2025-07-18 06:56:11,347 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 106, Total: 280
2025-07-18 06:56:11,348 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 157 (char 156)
2025-07-18 06:56:11,349 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:56:11,350 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 10.34s - 4 prompts executed concurrently
2025-07-18 06:56:11,351 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (84.6%) - Decision based on weighted votes: LONG (84.6%)
2025-07-18 06:56:11,351 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (84.6%) - Decision based on weighted votes: LONG (84.6%)
2025-07-18 06:56:11,351 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.1, WAIT: 0.6
2025-07-18 06:56:31,034 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:56:31,036 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:56:31,036 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:56:31,036 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:56:31,037 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:56:31,037 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-18 06:56:31,037 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:56:31,037 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:56:31,037 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.240550/$0.240551
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.239276 (Distance: 0.50%)
Resistance: $0.241680
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:56:31,038 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:56:34,990 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 216 chars
2025-07-18 06:56:34,990 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 60,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Neutral signals with favorable spread and no immediate volume spike"
}
```...
2025-07-18 06:56:34,990 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 95, Total: 937
2025-07-18 06:56:34,991 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:56:34,991 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:56:34,991 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:56:34,991 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:56:36,133 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 150 chars
2025-07-18 06:56:36,133 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "regime": "RANGING_TIGHT",
  "confidence": 75,
  "scalpability": "MEDIUM",
  "recommended_timeframe": "1m",
  "risk_level": "Medium"
}
```...
2025-07-18 06:56:36,133 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 70, Total: 666
2025-07-18 06:56:36,134 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalpability', 'recommended_timeframe', 'risk_level']
2025-07-18 06:56:36,134 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALPABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:56:36,136 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:56:36,136 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:56:36,138 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.10s - 2 prompts executed concurrently
2025-07-18 06:56:36,139 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (72.7%) - Decision based on weighted votes: LONG (72.7%)
2025-07-18 06:56:36,139 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (72.7%) - Decision based on weighted votes: LONG (72.7%)
2025-07-18 06:56:36,139 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.5, WAIT: 0.6
2025-07-18 06:57:01,092 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:57:01,093 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:57:01,094 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:57:01,094 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:57:01,094 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:57:01,094 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:57:01,094 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:57:01,094 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:57:01,095 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:57:01,095 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:57:01,095 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:57:01,095 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:57:01,095 - core.llm_orchestrator - INFO - 🚀 Submitted 5 prompts for parallel execution
2025-07-18 06:57:01,096 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:57:01,096 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:57:01,097 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.240285/$0.240286
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.239114 (Distance: 0.50%)
Resistance: $0.241518
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:57:01,097 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:57:01,098 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.240316 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:57:01,098 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:57:01,098 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-18 06:57:01,099 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:57:01,099 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:57:04,617 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 232 chars
2025-07-18 06:57:04,617 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
    "regime": "RANGING_TIGHT",
    "confidence": 75,
    "scalpability": "MEDIUM",
    "recommended_timeframe": "1m",
    "risk_level": "MEDIUM",
    "reasoning": "Dynamic regime detection based on volatility and trend analysis"
}...
2025-07-18 06:57:04,617 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 86, Total: 682
2025-07-18 06:57:04,618 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalpability', 'recommended_timeframe', 'risk_level', 'reasoning']
2025-07-18 06:57:04,618 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALPABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-18 06:57:04,618 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:57:04,619 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:57:07,953 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 811 chars
2025-07-18 06:57:07,954 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 85%, TAKE_PROFIT: 70%, STOP_LOSS: 60%, EXPLANATION: Given the account's healthy status and normal trading parameters along with a high-quality setup for DOGE/USDT which has shown strong alignment in momentum, it is recommended to take advantage of this opportunity. The co...
2025-07-18 06:57:07,954 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 938, Completion: 201, Total: 1139
2025-07-18 06:57:07,954 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:57:07,954 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 70.0, 'STOP_LOSS': 60.0, 'EXPLANATION': "GIVEN THE ACCOUNT'S HEALTHY STATUS AND NORMAL TRADING PARAMETERS ALONG WITH A HIGH-QUALITY SETUP FOR DOGE/USDT WHICH HAS SHOWN STRONG ALIGNMENT IN MOMENTUM, IT IS RECOMMENDED TO TAKE ADVANTAGE OF THIS OPPORTUNITY. THE CONFIDENCE LEVEL AT AN OPTIMISTIC BUT CAUTIOUS 85% REFLECTS BOTH POTENTIAL PROFITABILITY AS WELL AS THE NEED FOR RISK MANAGEMENT DUE TO MODERATE MARKET VOLATILITY AND ACCOUNT PRESERVATION CRITERIA. A CONSERVATIVE STOP LOSS HAS BEEN SET BELOW THE CURRENT PRICE BY 60%, WHILE A TAKE-PROFIT IS PLACED AT AN AMBITIOUS BUT REALISTIC LEVEL OF 70% ABOVE, ENSURING THAT POTENTIAL GAINS ARE MAXIMIZED WITHOUT COMPROMISING ON RISK MANAGEMENT PRINCIPLES ESSENTIAL FOR MAINTAINING ACCOUNT HEALTH IN NORMAL TRADING CONDITIONS.", 'ACTION': 'ENTER_NOW'}
2025-07-18 06:57:07,954 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:57:07,954 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-18 06:57:07,955 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:57:09,573 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 201 chars
2025-07-18 06:57:09,573 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Neutral signals but favorable spread and key levels near support"
}...
2025-07-18 06:57:09,573 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 89, Total: 931
2025-07-18 06:57:09,574 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:57:09,574 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:57:09,574 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:57:09,574 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:57:10,886 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 213 chars
2025-07-18 06:57:10,887 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level at +15 pip move.",
  "take_profit": 2.5,
  "stop_loss": 1.7,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:57:10,887 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 93, Total: 267
2025-07-18 06:57:10,888 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:57:10,888 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:57:10,889 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:57:10,889 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:57:12,519 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 343 chars
2025-07-18 06:57:12,519 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"risk_adjustment":1.5, "hold_time_target":30, "entry_threshold":75, "exit_threshold":65, "sizing_method":"VARIABLE", "reasoning":"Increase risk slightly due to recent poor performance and maintain higher entry threshold for better confidence in trades. Shorter hold time targeted at 30 seconds align...
2025-07-18 06:57:12,519 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 100, Total: 719
2025-07-18 06:57:12,520 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning', 'confidence']
2025-07-18 06:57:12,520 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-18 06:57:12,520 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.5x
2025-07-18 06:57:12,520 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.5x, Hold time 30min
2025-07-18 06:57:12,520 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-18 06:57:12,521 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 11.43s - 5 prompts executed concurrently
2025-07-18 06:57:12,522 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:57:12,523 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:57:12,523 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.5, WAIT: 3.5
2025-07-18 06:57:16,836 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $18.50, Required Margin: $0.93, Account Balance: $33.37
2025-07-18 06:57:16,836 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:57:16,837 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:57:19,137 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:58:23,361 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:58:23,363 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:58:23,364 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:58:23,364 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:58:23,364 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:58:23,365 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:58:23,365 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:58:23,365 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:58:23,366 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-18 06:58:23,366 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:58:23,370 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:58:23,373 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.240193/$0.240194
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.238909 (Distance: 0.50%)
Resistance: $0.241311
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:58:23,372 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 06:58:23,371 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:58:23,373 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:58:23,374 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.240110 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 06:58:23,374 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:58:23,376 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:58:27,193 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 266 chars
2025-07-18 06:58:27,194 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with increasing volume and positive candlestick pattern (bullish engulfing)",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 06:58:27,194 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 104, Total: 278
2025-07-18 06:58:27,194 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:58:27,194 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:58:27,194 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:58:27,194 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:58:28,686 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 211 chars
2025-07-18 06:58:28,687 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"REGIME": "RANGING_TIGHT", "CONFIDENCE": 75, "SCALP_SUITABILITY": "MEDIUM", "RECOMMENDED_TIMEFRAME": "1m", "RISK_LEVEL": "MEDIUM", "REASONING": "Dynamic regime detection based on volatility and trend analysis"}...
2025-07-18 06:58:28,687 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 90, Total: 686
2025-07-18 06:58:28,688 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-18 06:58:28,688 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-18 06:58:28,688 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:58:28,689 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:58:31,874 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 770 chars
2025-07-18 06:58:31,875 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 85%, TAKE_PROFIT: 70%, STOP_LOSS: 60%, EXPLANATION: The current market conditions are neutral with normal volatility and average volume profile. Given the account's healthy status and adherence to preservation criteria, there is a strong opportunity for profit without exc...
2025-07-18 06:58:31,875 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 937, Completion: 189, Total: 1126
2025-07-18 06:58:31,876 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 06:58:31,878 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 70.0, 'STOP_LOSS': 60.0, 'EXPLANATION': "THE CURRENT MARKET CONDITIONS ARE NEUTRAL WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE. GIVEN THE ACCOUNT'S HEALTHY STATUS AND ADHERENCE TO PRESERVATION CRITERIA, THERE IS A STRONG OPPORTUNITY FOR PROFIT WITHOUT EXCESSIVE RISK EXPOSURE IN DOGE/USDT DUE TO ITS MEDIUM SETUP QUALITY INDICATORS LIKE MOM (+0.0%) AND HIGH LIQUIDITY (<0.2%). THE HISTORICAL CONTEXT SHOWS CONSISTENT PRICING WITH NO SIGNIFICANT DEVIATIONS THAT WOULD SUGGEST AN OVERBOUGHT OR OVERSOLD CONDITION, THUS A LONG POSITION IS RECOMMENDED FOR THE UPCOMING 5-15 MINUTE WINDOW TO CAPITALIZE ON POTENTIAL PRICE MOVEMENTS WITHIN CONSERVATIVE VOLATILITY PARAMETERS AND MAINTAIN RISK EXPOSURE UNDER HEALTHY ACCOUNT LIMITS.", 'ACTION': 'ENTER_NOW'}
2025-07-18 06:58:31,879 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 06:58:31,879 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-18 06:58:31,880 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 06:58:33,490 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 199 chars
2025-07-18 06:58:33,491 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Neutral signals but favorable spread and price near key levels"
}...
2025-07-18 06:58:33,491 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 841, Completion: 89, Total: 930
2025-07-18 06:58:33,491 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:58:33,491 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:58:33,492 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:58:33,492 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:58:33,493 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 10.13s - 4 prompts executed concurrently
2025-07-18 06:58:33,493 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (51.4%) - Decision based on weighted votes: LONG (51.4%)
2025-07-18 06:58:33,494 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (51.4%) - Decision based on weighted votes: LONG (51.4%)
2025-07-18 06:58:33,494 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.5, WAIT: 3.3
2025-07-18 06:58:37,527 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $17.75, Required Margin: $0.89, Account Balance: $32.01
2025-07-18 06:58:37,528 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:58:37,528 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:58:39,926 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 06:59:23,263 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 06:59:23,264 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:59:23,265 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:59:23,265 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:59:23,265 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 06:59:23,266 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:59:23,266 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:59:23,267 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:59:23,266 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:59:23,266 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-18 06:59:23,267 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 06:59:23,266 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 06:59:23,267 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.240185/$0.240186
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.238954 (Distance: 0.50%)
Resistance: $0.241356
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 06:59:23,268 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:59:23,268 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 06:59:23,269 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:59:23,270 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-18 06:59:23,271 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 06:59:26,707 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 148 chars
2025-07-18 06:59:26,707 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
    "regime": "RANGING_TIGHT",
    "confidence": 85,
    "scalpability": "Medium",
    "recommended_timeframe": "1m",
    "risk_level": "Medium"
}...
2025-07-18 06:59:26,708 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 64, Total: 660
2025-07-18 06:59:26,708 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalpability', 'recommended_timeframe', 'risk_level']
2025-07-18 06:59:26,708 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALPABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 06:59:26,709 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 06:59:26,709 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 06:59:30,695 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1138 chars
2025-07-18 06:59:30,695 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "risk_adjustment": [0.5, 2.0],
  "hold_time_target": "8", // in minutes as specified by the user's current strategy average hold time of 8 min
  "entry_threshold": 70, // maintaining at a high confidence level for entry decisions to sustain performance despite downward trend indication.
...
2025-07-18 06:59:30,695 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 284, Total: 903
2025-07-18 06:59:30,696 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning', 'confidence']
2025-07-18 06:59:30,696 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-18 06:59:30,696 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-18 06:59:30,696 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-18 06:59:30,696 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-18 06:59:31,951 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 213 chars
2025-07-18 06:59:31,951 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance at +15 pip on EUR/USD.",
  "take_profit": 2.3,
  "stop_loss": 1.8,
  "hold_time": "4min",
  "leverage": 30
}
```...
2025-07-18 06:59:31,951 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 97, Total: 271
2025-07-18 06:59:31,952 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 06:59:31,952 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 06:59:31,952 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 06:59:31,953 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 06:59:33,393 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 204 chars
2025-07-18 06:59:33,393 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Price near support level with favorable spread and neutral momentum"
}...
2025-07-18 06:59:33,393 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 87, Total: 929
2025-07-18 06:59:33,394 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:59:33,394 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 06:59:33,394 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 06:59:33,394 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 06:59:33,395 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 10.13s - 4 prompts executed concurrently
2025-07-18 06:59:33,395 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:59:33,396 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 06:59:33,396 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.8, WAIT: 3.5
2025-07-18 06:59:37,677 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $17.15, Required Margin: $0.86, Account Balance: $30.94
2025-07-18 06:59:37,678 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 06:59:37,679 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 06:59:39,982 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 07:00:22,827 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 07:00:22,828 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 07:00:22,828 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 07:00:22,829 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 07:00:22,829 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 07:00:22,829 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 07:00:22,829 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 07:00:22,829 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 07:00:22,831 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 07:00:22,830 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 07:00:22,832 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.240405/$0.240406
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.239177 (Distance: 0.50%)
Resistance: $0.241581
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 07:00:22,832 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 07:00:22,832 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 07:00:22,830 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-18 07:00:22,831 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 07:00:22,834 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-18 07:00:22,834 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.240379 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-18 07:00:22,835 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 07:00:26,453 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 155 chars
2025-07-18 07:00:26,453 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 85,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM"
}
```...
2025-07-18 07:00:26,454 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 88, Total: 684
2025-07-18 07:00:26,455 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 07:00:26,455 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-18 07:00:26,455 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 07:00:26,456 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 07:00:27,658 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 221 chars
2025-07-18 07:00:27,658 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 92,
  "entry_reason": "Breakout above resistance level with strong volume indicators.",
  "take_profit": 1.5,
  "stop_loss": 0.3,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 07:00:27,658 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 92, Total: 266
2025-07-18 07:00:27,659 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 07:00:27,659 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 07:00:27,659 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 07:00:27,659 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 07:00:29,145 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 202 chars
2025-07-18 07:00:29,145 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 68,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 0,
  "REASONING": "Neutral signals but favorable spread and price near support level."
}...
2025-07-18 07:00:29,146 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 88, Total: 930
2025-07-18 07:00:29,146 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 07:00:29,147 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 07:00:29,147 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 07:00:29,148 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 07:00:32,576 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 813 chars
2025-07-18 07:00:32,577 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 85%, TAKE_PROFIT: 10%, STOP_LOSS: -15%, EXPLANATION: The DOGE/USDT pair shows a neutral sector momentum and normal overall volatility with an average volume profile. Given the account's healthy status, we have room for risk within our budgeted limits (2% per trade). With ...
2025-07-18 07:00:32,577 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 938, Completion: 208, Total: 1146
2025-07-18 07:00:32,578 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-18 07:00:32,578 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 10.0, 'STOP_LOSS': -15.0, 'EXPLANATION': "THE DOGE/USDT PAIR SHOWS A NEUTRAL SECTOR MOMENTUM AND NORMAL OVERALL VOLATILITY WITH AN AVERAGE VOLUME PROFILE. GIVEN THE ACCOUNT'S HEALTHY STATUS, WE HAVE ROOM FOR RISK WITHIN OUR BUDGETED LIMITS (2% PER TRADE). WITH HIGH SETUP QUALITY INDICATED BY STABLE MOM (+0.0%) AND GOOD LIQUIDITY (<0.2% SPREAD), COMBINED WITH A CONSERVATIVE TRADING RANGE PREFERENCE OF 1-3%, THIS OPPORTUNITY ALIGNS WELL UNDER THE HEALTH-ADJUSTED CRITERIA, ESPECIALLY CONSIDERING THAT THERE'S NO RECENT SIGNIFICANT PRICE MOVEMENT OR ADVERSE SIGNALS TO SUGGEST OTHERWISE. A TAKE PROFIT AT +10% AND STOP LOSS AT -15% ARE SET BASED ON TYPICAL CONSERVATIVE ENTRY POINTS FOR SUCH A MARKET CONDITION WHILE MAINTAINING ACCOUNT SAFETY AS PER PRESERVATION CRITERIA.", 'ACTION': 'ENTER_NOW'}
2025-07-18 07:00:32,578 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-18 07:00:32,578 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-18 07:00:32,579 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-18 07:00:32,580 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 9.75s - 4 prompts executed concurrently
2025-07-18 07:00:32,580 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (50.0%) - Decision based on weighted votes: LONG (50.0%)
2025-07-18 07:00:32,581 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (50.0%) - Decision based on weighted votes: LONG (50.0%)
2025-07-18 07:00:32,581 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.4, WAIT: 3.4
2025-07-18 07:00:36,514 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $16.87, Required Margin: $0.84, Account Balance: $30.00
2025-07-18 07:00:36,515 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 07:00:36,515 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 07:00:39,448 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 07:01:22,626 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-18 07:01:22,628 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 07:01:22,628 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 07:01:22,629 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-18 07:01:22,629 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 07:01:22,630 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 07:01:22,630 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-18 07:01:22,631 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 07:01:22,631 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-18 07:01:22,632 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-18 07:01:22,633 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-18 07:01:22,635 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.240729/$0.240730
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.239487 (Distance: 0.50%)
Resistance: $0.241893
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-18 07:01:22,636 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 07:01:22,637 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-18 07:01:26,723 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 202 chars
2025-07-18 07:01:26,724 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Neutral signal but favorable spread and recent price near support"
}...
2025-07-18 07:01:26,724 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 841, Completion: 89, Total: 930
2025-07-18 07:01:26,724 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 07:01:26,725 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-18 07:01:26,725 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-18 07:01:26,725 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-18 07:01:28,352 - main - INFO - Epinnox v6 starting up...
2025-07-18 07:01:28,356 - core.performance_monitor - INFO - Performance monitoring started
2025-07-18 07:01:28,356 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-18 07:01:28,357 - main - INFO - Performance monitoring initialized
2025-07-18 07:01:28,371 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-18 07:01:28,373 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-18 07:01:28,374 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-18 07:01:28,394 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 225 chars
2025-07-18 07:01:28,394 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Dynamic regime detection based on volatility and trend analysis"
}...
2025-07-18 07:01:28,395 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 105, Total: 701
2025-07-18 07:01:28,395 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-18 07:01:28,395 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-18 07:01:28,396 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-18 07:01:28,396 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-18 07:01:29,742 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 237 chars
2025-07-18 07:01:29,743 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke with a sudden upward trend indicating potential for quick profit.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-18 07:01:29,743 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 96, Total: 270
2025-07-18 07:01:29,743 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-18 07:01:29,743 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-18 07:01:29,744 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-18 07:01:29,744 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-18 07:01:29,745 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 7.12s - 3 prompts executed concurrently
2025-07-18 07:01:29,746 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 07:01:29,746 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%)
2025-07-18 07:01:29,746 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.8, WAIT: 3.3
2025-07-18 07:01:33,597 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-18 07:01:33,723 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $15.97, Required Margin: $0.80, Account Balance: $28.81
2025-07-18 07:01:33,723 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-18 07:01:33,724 - trading.real_trading_interface - INFO - 🔧 HTX CONTRACT CONVERSION: 1.******** DOGE → 1.******** contracts
2025-07-18 07:01:36,027 - core.risk_management_system - INFO - 📊 Position updated: DOGE/USDT:USDT_buy
2025-07-18 07:01:49,040 - config.autonomous_config - INFO - Configuration saved to configs/autonomous_trading.yaml
